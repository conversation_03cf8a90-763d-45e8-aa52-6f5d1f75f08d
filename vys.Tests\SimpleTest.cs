using Xunit;

namespace vys.Tests
{
    public class SimpleTest
    {
        [Fact]
        public void SimpleTest_ShouldPass()
        {
            // Arrange
            var expected = 2;
            
            // Act
            var actual = 1 + 1;
            
            // Assert
            Assert.Equal(expected, actual);
        }
        
        [Fact]
        public void SimpleTest_StringTest_ShouldPass()
        {
            // Arrange
            var expected = "Hello World";
            
            // Act
            var actual = "Hello" + " " + "World";
            
            // Assert
            Assert.Equal(expected, actual);
        }
    }
}
