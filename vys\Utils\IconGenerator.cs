using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using vys.Logging;

namespace vys.Utils
{
    /// <summary>
    /// Utility class for generating icon variants
    /// </summary>
    public static class IconGenerator
    {
        /// <summary>
        /// Creates a simple red icon variant
        /// </summary>
        public static void CreateRedIcon()
        {
            try
            {
                // Ensure Resources directory exists
                Directory.CreateDirectory("Resources");

                // Create a simple red icon (32x32)
                using (var bitmap = new Bitmap(32, 32))
                using (var graphics = Graphics.FromImage(bitmap))
                {
                    // Fill with red background
                    graphics.Clear(Color.Red);

                    // Add a simple "!" symbol in white
                    using (var font = new Font("Arial", 20, FontStyle.Bold))
                    using (var brush = new SolidBrush(Color.White))
                    {
                        var stringFormat = new StringFormat
                        {
                            Alignment = StringAlignment.Center,
                            LineAlignment = StringAlignment.Center
                        };

                        graphics.DrawString("!", font, brush,
                            new RectangleF(0, 0, 32, 32), stringFormat);
                    }

                    // Save as PNG
                    var pngPath = Path.Combine("Resources", "app_icon_red.png");
                    bitmap.Save(pngPath, ImageFormat.Png);
                    Logger.Info("IconGenerator", $"Created red icon PNG: {pngPath}");
                }

                // Create ICO file from PNG
                CreateIcoFromPng("Resources/app_icon_red.png", "Resources/app_icon_red.ico");
            }
            catch (Exception ex)
            {
                Logger.Error("IconGenerator", ex, "Error creating red icon");
            }
        }
        
        /// <summary>
        /// Creates a simple green icon variant
        /// </summary>
        public static void CreateGreenIcon()
        {
            try
            {
                // Ensure Resources directory exists
                Directory.CreateDirectory("Resources");

                // Create a simple green icon (32x32)
                using (var bitmap = new Bitmap(32, 32))
                using (var graphics = Graphics.FromImage(bitmap))
                {
                    // Fill with green background
                    graphics.Clear(Color.Green);

                    // Add a simple "✓" symbol in white
                    using (var font = new Font("Arial", 16, FontStyle.Bold))
                    using (var brush = new SolidBrush(Color.White))
                    {
                        var stringFormat = new StringFormat
                        {
                            Alignment = StringAlignment.Center,
                            LineAlignment = StringAlignment.Center
                        };

                        graphics.DrawString("✓", font, brush,
                            new RectangleF(0, 0, 32, 32), stringFormat);
                    }

                    // Save as PNG
                    var pngPath = Path.Combine("Resources", "app_icon_green.png");
                    bitmap.Save(pngPath, ImageFormat.Png);
                    Logger.Info("IconGenerator", $"Created green icon PNG: {pngPath}");
                }

                // Create ICO file from PNG
                CreateIcoFromPng("Resources/app_icon_green.png", "Resources/app_icon_green.ico");
            }
            catch (Exception ex)
            {
                Logger.Error("IconGenerator", ex, "Error creating green icon");
            }
        }
        
        /// <summary>
        /// Simple ICO creation from PNG (basic implementation)
        /// </summary>
        private static void CreateIcoFromPng(string pngPath, string icoPath)
        {
            try
            {
                using (var bitmap = new Bitmap(pngPath))
                {
                    // For simplicity, we'll use the PNG as ICO
                    // In a production app, you'd want proper ICO format conversion
                    using (var icon = Icon.FromHandle(bitmap.GetHicon()))
                    {
                        using (var fileStream = new FileStream(icoPath, FileMode.Create))
                        {
                            icon.Save(fileStream);
                        }
                    }
                }
                Logger.Info("IconGenerator", $"Created ICO file: {icoPath}");
            }
            catch (Exception ex)
            {
                Logger.Error("IconGenerator", ex, "Error creating ICO file");
            }
        }
    }
}
