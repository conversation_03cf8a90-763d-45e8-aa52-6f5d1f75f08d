using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using vys.Logging;

namespace vys.Utils.Audio
{
    public class AudioManager : IDisposable
    {
        private readonly WindowsAudioApi _audioApi;
        private readonly string _settingsPath;
        private bool _disposed = false;
        private AudioSettings _settings;

        public event EventHandler<MuteStateChangedEventArgs>? MuteStateChanged;

        public AudioManager()
        {
            _audioApi = new WindowsAudioApi();
            _settingsPath = InitializeSettingsPath();
            _settings = LoadSettings();

            EnsureDefaultMutedState();
            ApplyInitialMuteState();
        }

        private string InitializeSettingsPath()
        {
            var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            var appFolder = Path.Combine(appDataPath, "VYS Browser");
            Directory.CreateDirectory(appFolder);
            return Path.Combine(appFolder, "audio-settings.json");
        }

        private void EnsureDefaultMutedState()
        {
            if (!_settings.IsMuted)
            {
                Logger.Info("AudioManager", $"Forcing default mute state to True (was: {_settings.IsMuted})");
                _settings.IsMuted = true;
                SaveSettings();
            }
        }

        private void ApplyInitialMuteState()
        {
            Logger.Debug("AudioManager", $"Applying initial mute state: {_settings.IsMuted}");
            _audioApi.IsMuted = _settings.IsMuted;

            if (_settings.IsMuted)
            {
                Task.Run(() =>
                {
                    System.Threading.Thread.Sleep(1000);
                    Logger.Debug("AudioManager", $"Re-applying mute state after delay: {_settings.IsMuted}");
                    _audioApi.IsMuted = _settings.IsMuted;
                });
            }
        }

        public bool IsMuted => _audioApi.IsMuted;

        public float Volume
        {
            get => _audioApi.Volume;
            set
            {
                _audioApi.Volume = value;
                _settings.Volume = value;
                SaveSettings();
            }
        }

        public void ToggleMute()
        {
            var newMuteState = !IsMuted;
            SetMute(newMuteState);
        }

        public void SetMute(bool muted)
        {
            var oldState = IsMuted;

            Logger.Debug("AudioManager", $"Setting mute state to {muted}");

            _audioApi.IsMuted = muted;
            _settings.IsMuted = muted;
            SaveSettings();

            _audioApi.RefreshAudioSession();

            var newState = IsMuted;
            Logger.Info("AudioManager", $"Mute state changed from {oldState} to {newState}");

            OnMuteStateChanged(new MuteStateChangedEventArgs(muted));
        }

        public void RefreshAudioSession()
        {
            _audioApi.RefreshAudioSession();

            if (_settings.IsMuted)
            {
                _audioApi.IsMuted = true;
            }
        }

        private AudioSettings LoadSettings()
        {
            try
            {
                if (File.Exists(_settingsPath))
                {
                    var json = File.ReadAllText(_settingsPath);
                    var settings = JsonSerializer.Deserialize<AudioSettings>(json);
                    if (settings != null)
                    {
                        Logger.Debug("AudioManager", $"Loaded audio settings: IsMuted={settings.IsMuted}, Volume={settings.Volume}");
                        return settings;
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Warning("AudioManager", $"Failed to load audio settings: {ex.Message}");
            }

            return CreateAndSaveDefaultSettings();
        }

        private AudioSettings CreateAndSaveDefaultSettings()
        {
            var defaultSettings = new AudioSettings();
            Logger.Debug("AudioManager", $"Using default audio settings: IsMuted={defaultSettings.IsMuted}, Volume={defaultSettings.Volume}");

            try
            {
                var json = JsonSerializer.Serialize(defaultSettings, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(_settingsPath, json);
                Logger.Debug("AudioManager", $"Saved default settings to {_settingsPath}");
            }
            catch (Exception ex)
            {
                Logger.Warning("AudioManager", $"Failed to save default settings: {ex.Message}");
            }

            return defaultSettings;
        }

        private void SaveSettings()
        {
            try
            {
                var json = JsonSerializer.Serialize(_settings, new JsonSerializerOptions 
                { 
                    WriteIndented = true 
                });
                File.WriteAllText(_settingsPath, json);
            }
            catch (Exception ex)
            {
                Logger.Warning("AudioManager", $"Failed to save audio settings: {ex.Message}");
            }
        }

        protected virtual void OnMuteStateChanged(MuteStateChangedEventArgs e)
        {
            MuteStateChanged?.Invoke(this, e);
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _audioApi?.Dispose();
                _disposed = true;
            }
        }
    }

    public class AudioSettings
    {
        public bool IsMuted { get; set; } = true;
        public float Volume { get; set; } = 1.0f;
        public DateTime LastUpdated { get; set; } = DateTime.Now;
    }

    public class MuteStateChangedEventArgs : EventArgs
    {
        public bool IsMuted { get; }

        public MuteStateChangedEventArgs(bool isMuted)
        {
            IsMuted = isMuted;
        }
    }
}
