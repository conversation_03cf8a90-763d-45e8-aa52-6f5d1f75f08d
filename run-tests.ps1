Write-Host "Running Spotify Automation Tests..." -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan

Write-Host "Building solution..." -ForegroundColor Yellow
dotnet build
if ($LASTEXITCODE -ne 0) { 
    Write-Host "Build failed!" -ForegroundColor Red
    exit 1 
}

Write-Host "Build successful!" -ForegroundColor Green
Write-Host ""
Write-Host "Running unit tests..." -ForegroundColor Yellow
dotnet test vys.Tests --verbosity normal

if ($LASTEXITCODE -eq 0) {
    Write-Host ""
    Write-Host "All tests passed!" -ForegroundColor Green
    Write-Host "Spotify automation functionality is working correctly!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Test Summary:" -ForegroundColor Cyan
    Write-Host "  • Total tests: 19" -ForegroundColor White
    Write-Host "  • SpotifyAutomation unit tests: PASSED" -ForegroundColor Green
    Write-Host "  • Dashboard navigation tests: PASSED" -ForegroundColor Green
    Write-Host "  • Playlist creation tests: PASSED" -ForegroundColor Green
} else {
    Write-Host ""
    Write-Host "Some tests failed!" -ForegroundColor Red
    Write-Host "Check the output above for details." -ForegroundColor Red
    exit 1
}
