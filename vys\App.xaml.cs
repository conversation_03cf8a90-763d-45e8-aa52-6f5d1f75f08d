﻿using System.Windows;
using System.IO;
using vys.Logging;
using vys.Utils;

namespace vys;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : System.Windows.Application
{
    protected override void OnStartup(StartupEventArgs e)
    {
        // Parse command line arguments
        bool isDebugMode = ParseCommandLineArguments(e.Args);

        // Initialize logging system
        var logFilePath = isDebugMode ? Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "VYS Browser", "debug.log") : null;
        Logger.Initialize(isDebugMode, logFilePath);

        Logger.Info("Application", "VYS Browser starting with WebView2...");
        Logger.Info("Application", "WebView2 has built-in H.264/AAC codec support!");

        try
        {
            // Generate icon variants if they don't exist
            if (!System.IO.File.Exists("Resources/app_icon_red.png"))
            {
                Logger.Info("Application", "Generating icon variants...");
                IconGenerator.CreateRedIcon();
                IconGenerator.CreateGreenIcon();
            }

            base.OnStartup(e);
            Logger.Info("Application", "WPF application with WebView2 started successfully.");
        }
        catch (Exception ex)
        {
            Logger.Error("Application", ex, "Failed to start application");
            Environment.Exit(1);
        }
    }

    /// <summary>
    /// Parse command line arguments to determine debug mode
    /// </summary>
    private bool ParseCommandLineArguments(string[] args)
    {
        bool isDebugMode = false;

        foreach (var arg in args)
        {
            switch (arg.ToLower())
            {
                case "--debug":
                case "-d":
                case "--verbose":
                case "-v":
                    isDebugMode = true;
                    break;
                case "--help":
                case "-h":
                    ShowHelp();
                    Environment.Exit(0);
                    break;
            }
        }

        return isDebugMode;
    }

    /// <summary>
    /// Show command line help
    /// </summary>
    private void ShowHelp()
    {
        Console.WriteLine("VYS Browser - WebView2-based Browser Application");
        Console.WriteLine();
        Console.WriteLine("Usage: vys.exe [options]");
        Console.WriteLine();
        Console.WriteLine("Options:");
        Console.WriteLine("  --debug, -d     Enable debug mode with verbose logging");
        Console.WriteLine("  --verbose, -v   Same as --debug");
        Console.WriteLine("  --help, -h      Show this help message");
        Console.WriteLine();
        Console.WriteLine("Examples:");
        Console.WriteLine("  vys.exe                 # Run in production mode (minimal logging)");
        Console.WriteLine("  vys.exe --debug         # Run in debug mode (verbose logging)");
        Console.WriteLine("  vys.exe -d              # Short form of debug mode");
        Console.WriteLine();
        Console.WriteLine("Debug mode enables:");
        Console.WriteLine("  - Detailed audio session monitoring");
        Console.WriteLine("  - Mute button operation logging");
        Console.WriteLine("  - Spotify login detection details");
        Console.WriteLine("  - WebView2 process tracking");
        Console.WriteLine("  - Log file creation in %APPDATA%\\VYS Browser\\debug.log");
    }

    protected override void OnExit(ExitEventArgs e)
    {
        Logger.Info("Application", "VYS Browser shutting down...");
        base.OnExit(e);
    }

}

