﻿<Window x:Class="vys.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:vys"
        xmlns:wv2="clr-namespace:Microsoft.Web.WebView2.Wpf;assembly=Microsoft.Web.WebView2.Wpf"
        mc:Ignorable="d"
        Title="VYS Browser - WebView2" Height="800" Width="1200" WindowState="Normal"
        Icon="Resources/app_icon.ico">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header with Automation, <PERSON> and <PERSON><PERSON> -->
        <Grid Grid.Row="0" Background="#2D2D30" Height="120">
            <Grid.RowDefinitions>
                <RowDefinition Height="40"/>
                <RowDefinition Height="40"/>
                <RowDefinition Height="40"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- First Row: Main Controls -->
            <StackPanel Grid.Row="0" Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Left" VerticalAlignment="Center" Margin="10,0">
                <TextBox x:Name="PlaylistNameTextBox" Width="150" Height="25" Margin="5"
                         Background="#3C3C3C" Foreground="White" BorderBrush="#555"
                         Text="My New Playlist" VerticalContentAlignment="Center"
                         ToolTip="Enter playlist name" GotFocus="PlaylistNameTextBox_GotFocus" LostFocus="PlaylistNameTextBox_LostFocus"/>
                <Button x:Name="CreatePlaylistButton" Content="➕ Create" Width="80" Height="30" Margin="5"
                        Background="#1DB954" Foreground="White" BorderBrush="#1DB954" Click="CreatePlaylistButton_Click"
                        ToolTip="Create new playlist"/>
                <Button x:Name="DeletePlaylistButton" Content="🗑️ Delete" Width="80" Height="30" Margin="5"
                        Background="#E22134" Foreground="White" BorderBrush="#E22134" Click="DeletePlaylistButton_Click"
                        ToolTip="Delete playlist"/>

                <TextBox x:Name="SearchTextBox" Width="150" Height="25" Margin="5"
                         Background="#3C3C3C" Foreground="White" BorderBrush="#555"
                         Text="Search for songs..." VerticalContentAlignment="Center"
                         ToolTip="Enter search term" GotFocus="SearchTextBox_GotFocus" LostFocus="SearchTextBox_LostFocus"/>
                <Button x:Name="SearchButton" Content="🔍 Search" Width="80" Height="30" Margin="5"
                        Background="#3C3C3C" Foreground="White" BorderBrush="#555" Click="SearchButton_Click"
                        ToolTip="Search Spotify"/>

                <Button x:Name="DashboardButton" Content="🏠 Home" Width="80" Height="30" Margin="5"
                        Background="#3C3C3C" Foreground="White" BorderBrush="#555" Click="DashboardButton_Click"
                        ToolTip="Go to Spotify dashboard"/>
            </StackPanel>

            <!-- Second Row: Playlist Management -->
            <StackPanel Grid.Row="1" Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Left" VerticalAlignment="Center" Margin="10,0">
                <Label Content="Add Song:" Foreground="White" VerticalAlignment="Center" Margin="5,0" FontSize="11"/>
                <TextBox x:Name="SongNameTextBox" Width="150" Height="25" Margin="5"
                         Background="#3C3C3C" Foreground="White" BorderBrush="#555"
                         Text="brisa metamorfose" VerticalContentAlignment="Center"
                         ToolTip="Enter song name to add to playlist" GotFocus="SongNameTextBox_GotFocus" LostFocus="SongNameTextBox_LostFocus"/>
                <TextBox x:Name="TargetPlaylistTextBox" Width="120" Height="25" Margin="5"
                         Background="#3C3C3C" Foreground="White" BorderBrush="#555"
                         Text="Hello" VerticalContentAlignment="Center"
                         ToolTip="Enter playlist name to add song to" GotFocus="TargetPlaylistTextBox_GotFocus" LostFocus="TargetPlaylistTextBox_LostFocus"/>
                <Button x:Name="AddSongButton" Content="➕ Add to Playlist" Width="110" Height="30" Margin="5"
                        Background="#FF6B35" Foreground="White" BorderBrush="#FF6B35" Click="AddSongButton_Click"
                        ToolTip="Search for song and add to playlist"/>

                <Separator Width="20" Background="Transparent"/>

                <Label Content="Remove Song:" Foreground="White" VerticalAlignment="Center" Margin="5,0" FontSize="11"/>
                <TextBox x:Name="RemoveSongNameTextBox" Width="150" Height="25" Margin="5"
                         Background="#3C3C3C" Foreground="White" BorderBrush="#555"
                         Text="Metamorfose" VerticalContentAlignment="Center"
                         ToolTip="Enter song name to remove from playlist" GotFocus="RemoveSongNameTextBox_GotFocus" LostFocus="RemoveSongNameTextBox_LostFocus"/>
                <TextBox x:Name="RemoveFromPlaylistTextBox" Width="120" Height="25" Margin="5"
                         Background="#3C3C3C" Foreground="White" BorderBrush="#555"
                         Text="Hello" VerticalContentAlignment="Center"
                         ToolTip="Enter playlist name to remove song from" GotFocus="RemoveFromPlaylistTextBox_GotFocus" LostFocus="RemoveFromPlaylistTextBox_LostFocus"/>
                <Button x:Name="RemoveSongButton" Content="➖ Remove from Playlist" Width="140" Height="30" Margin="5"
                        Background="#E22134" Foreground="White" BorderBrush="#E22134" Click="RemoveSongButton_Click"
                        ToolTip="Remove song from playlist"/>
            </StackPanel>

            <!-- Third Row: Quick Play and List Songs -->
            <StackPanel Grid.Row="2" Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Left" VerticalAlignment="Center" Margin="10,0">
                <TextBox x:Name="QuickPlayPlaylistTextBox" Width="150" Height="25" Margin="5"
                         Background="#3C3C3C" Foreground="White" BorderBrush="#1DB954"
                         Text="Hello" VerticalContentAlignment="Center"
                         ToolTip="Enter playlist name to play"
                         GotFocus="QuickPlayPlaylistTextBox_GotFocus" LostFocus="QuickPlayPlaylistTextBox_LostFocus"/>
                <Button x:Name="QuickPlayButton" Content="▶️ Play" Width="60" Height="25" Margin="5"
                        Background="#1DB954" Foreground="White" BorderBrush="#1DB954" Click="QuickPlayButton_Click"
                        ToolTip="Play playlist"/>

                <Separator Width="20" Background="Transparent"/>

                <TextBox x:Name="ListSongsPlaylistTextBox" Width="150" Height="25" Margin="5"
                         Background="#3C3C3C" Foreground="White" BorderBrush="#4A90E2"
                         Text="Hello" VerticalContentAlignment="Center"
                         ToolTip="Enter playlist name to list songs"
                         GotFocus="ListSongsPlaylistTextBox_GotFocus" LostFocus="ListSongsPlaylistTextBox_LostFocus"/>
                <Button x:Name="ListSongsButton" Content="📋 List Songs" Width="80" Height="25" Margin="5"
                        Background="#4A90E2" Foreground="White" BorderBrush="#4A90E2" Click="ListSongsButton_Click"
                        ToolTip="List all songs in playlist"/>

                <Separator Width="20" Background="Transparent"/>

                <Button x:Name="ShuffleButton" Content="🔀 Shuffle" Width="80" Height="25" Margin="5"
                        Background="#9B59B6" Foreground="White" BorderBrush="#9B59B6" Click="ShuffleButton_Click"
                        ToolTip="Enable shuffle for current playlist"/>

                <Button x:Name="RepeatButton" Content="🔁 Repeat" Width="80" Height="25" Margin="5"
                        Background="#E67E22" Foreground="White" BorderBrush="#E67E22" Click="RepeatButton_Click"
                        ToolTip="Enable repeat for current playlist"/>

                <Separator Width="20" Background="Transparent"/>

                <Button x:Name="ListPlaylistsButton" Content="📋 List Playlists" Width="100" Height="25" Margin="5"
                        Background="#8E44AD" Foreground="White" BorderBrush="#8E44AD" Click="ListPlaylistsButton_Click"
                        ToolTip="List all user playlists"/>

            </StackPanel>

            <!-- System Buttons -->
            <StackPanel Grid.Row="0" Grid.RowSpan="3" Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Center">
                <Button x:Name="MuteButton" Content="🔊" Width="40" Height="30" Margin="5"
                        Background="#3C3C3C" Foreground="White" BorderBrush="#555" Click="MuteButton_Click"
                        ToolTip="Mute/Unmute Audio (Ctrl+M)"/>
                <Button x:Name="DevToolsButton" Content="🔧" Width="40" Height="30" Margin="5"
                        Background="#3C3C3C" Foreground="White" BorderBrush="#555" Click="DevToolsButton_Click"
                        ToolTip="Toggle Developer Tools"/>
            </StackPanel>
        </Grid>

        <!-- Main Content Area with Splitter -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="0" x:Name="DevToolsColumn"/>
            </Grid.ColumnDefinitions>

            <!-- Browser Control -->
            <wv2:WebView2 x:Name="Browser" Grid.Column="0"/>

            <!-- Splitter -->
            <GridSplitter x:Name="DevToolsSplitter" Grid.Column="1" Width="5"
                         Background="#555" HorizontalAlignment="Center" VerticalAlignment="Stretch"
                         Visibility="Collapsed"/>

            <!-- Developer Tools Panel -->
            <Grid x:Name="DevToolsPanel" Grid.Column="2" Background="#1E1E1E" Visibility="Collapsed">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Dev Tools Header -->
                <Grid Grid.Row="0" Background="#2D2D30" Height="30">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="Developer Tools" Foreground="White"
                              VerticalAlignment="Center" Margin="10,0"/>
                    <Button x:Name="CloseDevToolsButton" Grid.Column="1" Content="✕" Width="30" Height="25"
                           Background="Transparent" Foreground="White" BorderBrush="Transparent"
                           Click="CloseDevToolsButton_Click" ToolTip="Close Developer Tools"/>
                </Grid>

                <!-- Dev Tools WebView -->
                <wv2:WebView2 x:Name="DevToolsWebView" Grid.Row="1"/>
            </Grid>
        </Grid>

        <!-- Status Bar -->
        <Grid Grid.Row="2" Background="#2D2D30" Height="25">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            <TextBlock x:Name="StatusText" Grid.Column="0" Text="Checking Status..." Foreground="White"
                       VerticalAlignment="Center" Margin="10,0"/>
            <TextBlock Grid.Column="1" Text="VYS Browser v1.0" Foreground="#888"
                       VerticalAlignment="Center" Margin="10,0"/>
        </Grid>
    </Grid>
</Window>
