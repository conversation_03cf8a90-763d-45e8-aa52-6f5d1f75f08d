@echo off
echo Testing Shuffle Human Delays...
echo ===============================

echo.
echo Building test project only...
dotnet build vys.Tests --verbosity minimal
if %ERRORLEVEL% neq 0 (
    echo Test build failed!
    pause
    exit /b 1
)

echo.
echo Running shuffle delay tests...
dotnet test vys.Tests --filter "EnableShuffleAsync" --verbosity normal --logger "console;verbosity=detailed"

echo.
echo Test completed!
pause
