using System;
using System.IO;
using Xunit;
using vys.Utils;
using vys.Logging;

namespace vys.Tests
{
    /// <summary>
    /// Unit tests for IconGenerator utility
    /// </summary>
    public class IconGeneratorTests : IDisposable
    {
        private readonly string _testResourcesPath;

        public IconGeneratorTests()
        {
            Logger.Initialize(true);
            _testResourcesPath = Path.Combine(Path.GetTempPath(), "VysTestResources");
            
            // Clean up any existing test resources
            if (Directory.Exists(_testResourcesPath))
            {
                Directory.Delete(_testResourcesPath, true);
            }
            
            // Change to temp directory for testing
            Directory.SetCurrentDirectory(Path.GetTempPath());
        }

        public void Dispose()
        {
            // Clean up test resources
            try
            {
                if (Directory.Exists("Resources"))
                {
                    Directory.Delete("Resources", true);
                }
                if (Directory.Exists(_testResourcesPath))
                {
                    Directory.Delete(_testResourcesPath, true);
                }
            }
            catch (Exception)
            {
                // Ignore cleanup errors
            }
        }

        #region CreateRedIcon Tests

        [Fact]
        public void CreateRedIcon_ShouldCreateResourcesDirectory()
        {
            // Act
            IconGenerator.CreateRedIcon();

            // Assert
            Assert.True(Directory.Exists("Resources"), "Resources directory should be created");
        }

        [Fact]
        public void CreateRedIcon_ShouldCreatePngFile()
        {
            // Act
            IconGenerator.CreateRedIcon();

            // Assert
            var pngPath = Path.Combine("Resources", "app_icon_red.png");
            Assert.True(File.Exists(pngPath), $"PNG file should be created at {pngPath}");
        }

        [Fact]
        public void CreateRedIcon_ShouldCreateIcoFile()
        {
            // Act
            IconGenerator.CreateRedIcon();

            // Assert
            var icoPath = Path.Combine("Resources", "app_icon_red.ico");
            Assert.True(File.Exists(icoPath), $"ICO file should be created at {icoPath}");
        }

        [Fact]
        public void CreateRedIcon_PngFileShouldHaveValidSize()
        {
            // Act
            IconGenerator.CreateRedIcon();

            // Assert
            var pngPath = Path.Combine("Resources", "app_icon_red.png");
            var fileInfo = new FileInfo(pngPath);
            Assert.True(fileInfo.Length > 0, "PNG file should not be empty");
            Assert.True(fileInfo.Length < 10000, "PNG file should be reasonably sized (less than 10KB)");
        }

        [Fact]
        public void CreateRedIcon_IcoFileShouldHaveValidSize()
        {
            // Act
            IconGenerator.CreateRedIcon();

            // Assert
            var icoPath = Path.Combine("Resources", "app_icon_red.ico");
            var fileInfo = new FileInfo(icoPath);
            Assert.True(fileInfo.Length > 0, "ICO file should not be empty");
            Assert.True(fileInfo.Length < 50000, "ICO file should be reasonably sized (less than 50KB)");
        }

        [Fact]
        public void CreateRedIcon_ShouldNotThrowException()
        {
            // Act & Assert
            var exception = Record.Exception(() => IconGenerator.CreateRedIcon());
            Assert.Null(exception);
        }

        #endregion

        #region CreateGreenIcon Tests

        [Fact]
        public void CreateGreenIcon_ShouldCreateResourcesDirectory()
        {
            // Act
            IconGenerator.CreateGreenIcon();

            // Assert
            Assert.True(Directory.Exists("Resources"), "Resources directory should be created");
        }

        [Fact]
        public void CreateGreenIcon_ShouldCreatePngFile()
        {
            // Act
            IconGenerator.CreateGreenIcon();

            // Assert
            var pngPath = Path.Combine("Resources", "app_icon_green.png");
            Assert.True(File.Exists(pngPath), $"PNG file should be created at {pngPath}");
        }

        [Fact]
        public void CreateGreenIcon_ShouldCreateIcoFile()
        {
            // Act
            IconGenerator.CreateGreenIcon();

            // Assert
            var icoPath = Path.Combine("Resources", "app_icon_green.ico");
            Assert.True(File.Exists(icoPath), $"ICO file should be created at {icoPath}");
        }

        [Fact]
        public void CreateGreenIcon_PngFileShouldHaveValidSize()
        {
            // Act
            IconGenerator.CreateGreenIcon();

            // Assert
            var pngPath = Path.Combine("Resources", "app_icon_green.png");
            var fileInfo = new FileInfo(pngPath);
            Assert.True(fileInfo.Length > 0, "PNG file should not be empty");
            Assert.True(fileInfo.Length < 10000, "PNG file should be reasonably sized (less than 10KB)");
        }

        [Fact]
        public void CreateGreenIcon_IcoFileShouldHaveValidSize()
        {
            // Act
            IconGenerator.CreateGreenIcon();

            // Assert
            var icoPath = Path.Combine("Resources", "app_icon_green.ico");
            var fileInfo = new FileInfo(icoPath);
            Assert.True(fileInfo.Length > 0, "ICO file should not be empty");
            Assert.True(fileInfo.Length < 50000, "ICO file should be reasonably sized (less than 50KB)");
        }

        [Fact]
        public void CreateGreenIcon_ShouldNotThrowException()
        {
            // Act & Assert
            var exception = Record.Exception(() => IconGenerator.CreateGreenIcon());
            Assert.Null(exception);
        }

        #endregion

        #region Integration Tests

        [Fact]
        public void CreateBothIcons_ShouldCreateAllFiles()
        {
            // Act
            IconGenerator.CreateRedIcon();
            IconGenerator.CreateGreenIcon();

            // Assert
            Assert.True(File.Exists(Path.Combine("Resources", "app_icon_red.png")));
            Assert.True(File.Exists(Path.Combine("Resources", "app_icon_red.ico")));
            Assert.True(File.Exists(Path.Combine("Resources", "app_icon_green.png")));
            Assert.True(File.Exists(Path.Combine("Resources", "app_icon_green.ico")));
        }

        [Fact]
        public void CreateBothIcons_FilesShouldBeDifferent()
        {
            // Act
            IconGenerator.CreateRedIcon();
            IconGenerator.CreateGreenIcon();

            // Assert
            var redPngBytes = File.ReadAllBytes(Path.Combine("Resources", "app_icon_red.png"));
            var greenPngBytes = File.ReadAllBytes(Path.Combine("Resources", "app_icon_green.png"));
            
            Assert.NotEqual(redPngBytes, greenPngBytes);
        }

        [Fact]
        public void CreateIcons_MultipleCallsShouldNotFail()
        {
            // Act & Assert - Should not throw exceptions
            IconGenerator.CreateRedIcon();
            IconGenerator.CreateRedIcon(); // Second call should overwrite
            
            IconGenerator.CreateGreenIcon();
            IconGenerator.CreateGreenIcon(); // Second call should overwrite

            // Files should still exist
            Assert.True(File.Exists(Path.Combine("Resources", "app_icon_red.png")));
            Assert.True(File.Exists(Path.Combine("Resources", "app_icon_red.ico")));
            Assert.True(File.Exists(Path.Combine("Resources", "app_icon_green.png")));
            Assert.True(File.Exists(Path.Combine("Resources", "app_icon_green.ico")));
        }

        #endregion

        #region Error Handling Tests

        [Fact]
        public void CreateRedIcon_WithReadOnlyDirectory_ShouldHandleGracefully()
        {
            // Arrange
            Directory.CreateDirectory("Resources");
            
            try
            {
                // Try to make directory read-only (may not work on all systems)
                var dirInfo = new DirectoryInfo("Resources");
                dirInfo.Attributes |= FileAttributes.ReadOnly;

                // Act & Assert - Should not throw exception
                var exception = Record.Exception(() => IconGenerator.CreateRedIcon());
                Assert.Null(exception);
            }
            finally
            {
                // Clean up - remove read-only attribute
                try
                {
                    var dirInfo = new DirectoryInfo("Resources");
                    dirInfo.Attributes &= ~FileAttributes.ReadOnly;
                }
                catch { }
            }
        }

        [Fact]
        public void CreateGreenIcon_WithReadOnlyDirectory_ShouldHandleGracefully()
        {
            // Arrange
            Directory.CreateDirectory("Resources");
            
            try
            {
                // Try to make directory read-only (may not work on all systems)
                var dirInfo = new DirectoryInfo("Resources");
                dirInfo.Attributes |= FileAttributes.ReadOnly;

                // Act & Assert - Should not throw exception
                var exception = Record.Exception(() => IconGenerator.CreateGreenIcon());
                Assert.Null(exception);
            }
            finally
            {
                // Clean up - remove read-only attribute
                try
                {
                    var dirInfo = new DirectoryInfo("Resources");
                    dirInfo.Attributes &= ~FileAttributes.ReadOnly;
                }
                catch { }
            }
        }

        #endregion

        #region Performance Tests

        [Fact]
        public void CreateIcons_ShouldCompleteInReasonableTime()
        {
            // Arrange
            var startTime = DateTime.Now;

            // Act
            IconGenerator.CreateRedIcon();
            IconGenerator.CreateGreenIcon();

            // Assert
            var elapsed = DateTime.Now - startTime;
            Assert.True(elapsed.TotalSeconds < 10, $"Icon creation took too long: {elapsed.TotalSeconds} seconds");
        }

        #endregion
    }
}
