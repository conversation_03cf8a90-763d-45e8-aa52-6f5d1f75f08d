using System;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Microsoft.Web.WebView2.Core;
using vys;

namespace vys.Tests.Automation
{
    /// <summary>
    /// Unit tests for Spotify dashboard navigation functionality
    /// </summary>
    public class DashboardNavigationTests : IDisposable
    {
        private readonly ITestOutputHelper _output;
        private readonly SpotifyAutomation _spotifyAutomation;
        private readonly MockWebView2 _mockWebView;

        public DashboardNavigationTests(ITestOutputHelper output)
        {
            _output = output;
            _mockWebView = new MockWebView2();
            _spotifyAutomation = new SpotifyAutomation(_mockWebView);
        }

        [Fact]
        public async Task TestDashboardNavigation_ShouldReturnTrue_WhenToGetStartedElementExists()
        {
            // Arrange
            _output.WriteLine("Testing dashboard navigation with 'To get you started' element present");
            
            // Mock the JavaScript execution to return a successful dashboard state
            var mockDashboardResponse = @"{
                ""toGetStartedFound"": true,
                ""toGetStartedElement"": {
                    ""href"": ""/section/0JQ5DAob0JCuWaGLU6ntGf"",
                    ""testId"": ""see-all-link"",
                    ""draggable"": ""false"",
                    ""tabindex"": ""-1"",
                    ""textContent"": ""To get you started""
                },
                ""hasMainContent"": true,
                ""hasNavigation"": true,
                ""currentUrl"": ""https://open.spotify.com/""
            }";
            
            _mockWebView.SetScriptResponse(mockDashboardResponse);

            // Act
            var result = await _spotifyAutomation.TestDashboardNavigationAsync();

            // Assert
            Assert.True(result, "Dashboard navigation test should pass when 'To get you started' element is found");
            _output.WriteLine("✅ Test PASSED - Dashboard navigation successful");
        }

        [Fact]
        public async Task TestDashboardNavigation_ShouldReturnFalse_WhenToGetStartedElementMissing()
        {
            // Arrange
            _output.WriteLine("Testing dashboard navigation with 'To get you started' element missing");
            
            // Mock the JavaScript execution to return a failed dashboard state
            var mockDashboardResponse = @"{
                ""toGetStartedFound"": false,
                ""toGetStartedElement"": null,
                ""hasMainContent"": true,
                ""hasNavigation"": true,
                ""currentUrl"": ""https://open.spotify.com/search""
            }";
            
            _mockWebView.SetScriptResponse(mockDashboardResponse);

            // Act
            var result = await _spotifyAutomation.TestDashboardNavigationAsync();

            // Assert
            Assert.False(result, "Dashboard navigation test should fail when 'To get you started' element is not found");
            _output.WriteLine("✅ Test PASSED - Dashboard navigation correctly failed when element missing");
        }

        [Fact]
        public async Task TestGoToDashboard_ShouldNavigateAndVerify_WhenHomeButtonExists()
        {
            // Arrange
            _output.WriteLine("Testing comprehensive dashboard navigation (navigate + verify)");
            
            // Mock successful home button click and dashboard verification
            _mockWebView.SetMultipleScriptResponses(new[]
            {
                // First call: Debug info (button detection)
                @"{""totalButtons"": 10, ""homeRelatedButtons"": [{""testId"": ""home-button"", ""ariaLabel"": ""Home""}]}",
                
                // Second call: Home button click success
                @"{""success"": true, ""strategy"": ""testid"", ""element"": {""testId"": ""home-button"", ""ariaLabel"": ""Home""}}",
                
                // Third call: Dashboard verification
                @"{""toGetStartedFound"": true, ""toGetStartedElement"": {""textContent"": ""To get you started""}}"
            });

            // Act
            var result = await _spotifyAutomation.TestGoToDashboardAsync();

            // Assert
            Assert.True(result, "Comprehensive dashboard test should pass when navigation and verification succeed");
            _output.WriteLine("✅ Test PASSED - Comprehensive dashboard navigation successful");
        }

        [Fact]
        public async Task TestGoToDashboard_ShouldReturnFalse_WhenNavigationFails()
        {
            // Arrange
            _output.WriteLine("Testing comprehensive dashboard navigation when navigation fails");
            
            // Mock failed navigation (no home button found)
            _mockWebView.SetMultipleScriptResponses(new[]
            {
                // Debug info shows no home button
                @"{""totalButtons"": 5, ""homeRelatedButtons"": []}",
                
                // Home button click fails
                @"{""success"": false, ""error"": ""No home button found with any strategy""}"
            });

            // Act
            var result = await _spotifyAutomation.TestGoToDashboardAsync();

            // Assert
            Assert.False(result, "Comprehensive dashboard test should fail when navigation fails");
            _output.WriteLine("✅ Test PASSED - Comprehensive dashboard navigation correctly failed");
        }

        [Theory]
        [InlineData("To get you started")]
        [InlineData("TO GET YOU STARTED")]
        [InlineData("to get you started")]
        public async Task TestDashboardNavigation_ShouldHandleDifferentTextCasing(string elementText)
        {
            // Arrange
            _output.WriteLine($"Testing dashboard navigation with text casing: '{elementText}'");
            
            var mockResponse = $@"{{
                ""toGetStartedFound"": true,
                ""toGetStartedElement"": {{
                    ""textContent"": ""{elementText}""
                }}
            }}";
            
            _mockWebView.SetScriptResponse(mockResponse);

            // Act
            var result = await _spotifyAutomation.TestDashboardNavigationAsync();

            // Assert
            Assert.True(result, $"Dashboard navigation should work with text: '{elementText}'");
            _output.WriteLine($"✅ Test PASSED - Text casing '{elementText}' handled correctly");
        }

        [Fact]
        public async Task TestDashboardNavigation_ShouldHandleJavaScriptErrors()
        {
            // Arrange
            _output.WriteLine("Testing dashboard navigation with JavaScript errors");

            // Mock JavaScript error
            _mockWebView.SetScriptError(new InvalidOperationException("JavaScript execution failed"));

            // Act
            var result = await _spotifyAutomation.TestDashboardNavigationAsync();

            // Assert
            // The method should return false when JavaScript errors occur (it catches exceptions)
            Assert.False(result, "Dashboard navigation should return false when JavaScript errors occur");
            _output.WriteLine("✅ Test PASSED - JavaScript errors handled correctly by returning false");
        }

        public void Dispose()
        {
            _mockWebView?.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
