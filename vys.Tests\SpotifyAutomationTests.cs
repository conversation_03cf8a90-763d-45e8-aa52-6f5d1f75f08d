using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Moq;
using Xunit;
using vys;
using vys.Interfaces;
using vys.Logging;

namespace vys.Tests
{
    public class SpotifyAutomationTests
    {
        [Fact]
        public void SpotifyAutomation_Constructor_ShouldNotThrow()
        {
            // Arrange
            var mockWebView = new Mock<IWebView2Wrapper>();
            Logger.Initialize(true);

            // Act & Assert
            var automation = new SpotifyAutomation(mockWebView.Object);
            Assert.NotNull(automation);
        }

        [Fact]
        public async Task CreatePlaylistAsync_WithEmptyName_ShouldReturnFalse()
        {
            // Arrange
            var mockWebView = new Mock<IWebView2Wrapper>();
            Logger.Initialize(true);
            var automation = new SpotifyAutomation(mockWebView.Object);

            // Act
            var result = await automation.CreatePlaylistAsync("");

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task CreatePlaylistAsync_WithNullName_ShouldReturnFalse()
        {
            // Arrange
            var mockWebView = new Mock<IWebView2Wrapper>();
            Logger.Initialize(true);
            var automation = new SpotifyAutomation(mockWebView.Object);

            // Act
            var result = await automation.CreatePlaylistAsync(null!);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task CreatePlaylistAsync_WithValidName_SetsUpTokenInterception()
        {
            // Arrange
            var mockWebView = new Mock<IWebView2Wrapper>();
            Logger.Initialize(true);

            // Setup mock to return success for token interception, then fail early to avoid long wait
            mockWebView.SetupSequence(x => x.ExecuteScriptAsync(It.IsAny<string>()))
                      .ReturnsAsync("INTERCEPTION_SETUP") // Token interception succeeds
                      .ReturnsAsync("{}") // JS sources
                      .ReturnsAsync("false"); // Create button fails (to exit early)

            var automation = new SpotifyAutomation(mockWebView.Object);

            // Act
            var result = await automation.CreatePlaylistAsync("Test Playlist");

            // Assert
            Assert.False(result); // Should fail because create button fails

            // Verify that token interception was set up
            mockWebView.Verify(x => x.ExecuteScriptAsync(It.Is<string>(s =>
                s.Contains("capturedAuthToken") && s.Contains("capturedClientToken"))), Times.Once);
        }

        [Theory]
        [InlineData("Simple Playlist")]
        [InlineData("Playlist with Numbers 123")]
        [InlineData("John's Favorite Songs")]
        [InlineData("Special-Characters_Playlist!")]
        public async Task CreatePlaylistAsync_WithVariousNames_ShouldEscapeCorrectly(string playlistName)
        {
            // Arrange
            var mockWebView = new Mock<IWebView2Wrapper>();
            Logger.Initialize(true);

            // Setup to fail at create button to avoid long execution
            mockWebView.SetupSequence(x => x.ExecuteScriptAsync(It.IsAny<string>()))
                      .ReturnsAsync("INTERCEPTION_SETUP") // Token interception
                      .ReturnsAsync("{}") // JS sources
                      .ReturnsAsync("false"); // Create button fails - exits early

            var automation = new SpotifyAutomation(mockWebView.Object);

            // Act
            var result = await automation.CreatePlaylistAsync(playlistName);

            // Assert
            Assert.False(result); // Returns false because create button fails

            // Verify that the method was called (name escaping happens internally)
            // This test mainly verifies the method handles various name formats without crashing
            mockWebView.Verify(x => x.ExecuteScriptAsync(It.IsAny<string>()), Times.AtLeast(2));
        }

        [Fact]
        public async Task CreatePlaylistAsync_ScriptContent_ContainsTokenInterception()
        {
            // Arrange
            var mockWebView = new Mock<IWebView2Wrapper>();
            Logger.Initialize(true);

            // Capture all script calls
            var scriptCalls = new List<string>();
            mockWebView.Setup(x => x.ExecuteScriptAsync(It.IsAny<string>()))
                      .Callback<string>(script => scriptCalls.Add(script))
                      .ReturnsAsync("false"); // Fail early to avoid wait

            var automation = new SpotifyAutomation(mockWebView.Object);

            // Act
            await automation.CreatePlaylistAsync("Test Playlist");

            // Assert
            var allScripts = string.Join(" ", scriptCalls);

            // Verify key components that should always be present
            Assert.Contains("capturedAuthToken", allScripts);
            Assert.Contains("capturedClientToken", allScripts);
            Assert.True(scriptCalls.Count >= 2); // At least token interception and JS sources
        }

        [Fact]
        public async Task CreatePlaylistAsync_CreateButtonNotFound_ShouldReturnFalse()
        {
            // Arrange
            var mockWebView = new Mock<IWebView2Wrapper>();
            Logger.Initialize(true);

            // Setup first script (token interception) to succeed, but Create button to fail
            mockWebView.SetupSequence(x => x.ExecuteScriptAsync(It.IsAny<string>()))
                      .ReturnsAsync("INTERCEPTION_SETUP") // Token interception
                      .ReturnsAsync("{}") // JS sources
                      .ReturnsAsync("false"); // Create button fails

            var automation = new SpotifyAutomation(mockWebView.Object);

            // Act
            var result = await automation.CreatePlaylistAsync("Test Playlist");

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task CreatePlaylistAsync_AllStepsExecuted_VerifyScriptCalls()
        {
            // Arrange
            var mockWebView = new Mock<IWebView2Wrapper>();
            Logger.Initialize(true);

            // Setup to fail at create button to avoid long wait, but verify all expected script types
            mockWebView.SetupSequence(x => x.ExecuteScriptAsync(It.IsAny<string>()))
                      .ReturnsAsync("INTERCEPTION_SETUP") // Token interception
                      .ReturnsAsync("{}") // JS sources
                      .ReturnsAsync("false"); // Create button fails - exits early

            var automation = new SpotifyAutomation(mockWebView.Object);

            // Act
            var result = await automation.CreatePlaylistAsync("Test Playlist");

            // Assert
            Assert.False(result); // Returns false because create button fails

            // Verify that the expected scripts were called
            mockWebView.Verify(x => x.ExecuteScriptAsync(It.Is<string>(s => s.Contains("capturedAuthToken"))), Times.Once);
            mockWebView.Verify(x => x.ExecuteScriptAsync(It.IsAny<string>()), Times.AtLeast(3));
        }
    }
}
