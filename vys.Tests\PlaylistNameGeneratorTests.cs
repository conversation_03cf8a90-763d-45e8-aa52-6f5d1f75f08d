using System;
using System.Collections.Generic;
using System.Linq;
using Xunit;
using vys.Utils;
using vys.Logging;

namespace vys.Tests
{
    /// <summary>
    /// Unit tests for PlaylistNameGenerator utility
    /// </summary>
    public class PlaylistNameGeneratorTests
    {
        public PlaylistNameGeneratorTests()
        {
            Logger.Initialize(true);
        }

        #region GenerateRandomName Tests

        [Fact]
        public void GenerateRandomName_ShouldReturnNonEmptyString()
        {
            // Act
            var result = PlaylistNameGenerator.GenerateRandomName();

            // Assert
            Assert.NotNull(result);
            Assert.NotEmpty(result);
        }

        [Fact]
        public void GenerateRandomName_ShouldReturnDifferentNames()
        {
            // Act
            var names = new HashSet<string>();
            for (int i = 0; i < 20; i++)
            {
                names.Add(PlaylistNameGenerator.GenerateRandomName());
            }

            // Assert - Should have some variety (at least 10 different names out of 20)
            Assert.True(names.Count >= 10, $"Expected at least 10 different names, got {names.Count}");
        }

        [Fact]
        public void GenerateRandomName_ShouldNotReturnNullOrWhitespace()
        {
            // Act & Assert
            for (int i = 0; i < 10; i++)
            {
                var result = PlaylistNameGenerator.GenerateRandomName();
                Assert.False(string.IsNullOrWhiteSpace(result), $"Generated name should not be null or whitespace: '{result}'");
            }
        }

        [Fact]
        public void GenerateRandomName_ShouldReturnReasonableLength()
        {
            // Act
            var result = PlaylistNameGenerator.GenerateRandomName();

            // Assert - Should be between 5 and 50 characters (reasonable playlist name length)
            Assert.True(result.Length >= 5, $"Name too short: '{result}' (length: {result.Length})");
            Assert.True(result.Length <= 50, $"Name too long: '{result}' (length: {result.Length})");
        }

        #endregion

        #region GenerateNameByTheme Tests

        [Theory]
        [InlineData(PlaylistTheme.Mood)]
        [InlineData(PlaylistTheme.Time)]
        [InlineData(PlaylistTheme.Genre)]
        [InlineData(PlaylistTheme.Creative)]
        public void GenerateNameByTheme_ShouldReturnNonEmptyString(PlaylistTheme theme)
        {
            // Act
            var result = PlaylistNameGenerator.GenerateNameByTheme(theme);

            // Assert
            Assert.NotNull(result);
            Assert.NotEmpty(result);
            Assert.False(string.IsNullOrWhiteSpace(result));
        }

        [Fact]
        public void GenerateNameByTheme_MoodTheme_ShouldGenerateVariety()
        {
            // Act
            var names = new HashSet<string>();
            for (int i = 0; i < 15; i++)
            {
                names.Add(PlaylistNameGenerator.GenerateNameByTheme(PlaylistTheme.Mood));
            }

            // Assert
            Assert.True(names.Count >= 8, $"Expected at least 8 different mood names, got {names.Count}");
        }

        [Fact]
        public void GenerateNameByTheme_TimeTheme_ShouldGenerateVariety()
        {
            // Act
            var names = new HashSet<string>();
            for (int i = 0; i < 15; i++)
            {
                names.Add(PlaylistNameGenerator.GenerateNameByTheme(PlaylistTheme.Time));
            }

            // Assert
            Assert.True(names.Count >= 8, $"Expected at least 8 different time names, got {names.Count}");
        }

        [Fact]
        public void GenerateNameByTheme_GenreTheme_ShouldGenerateVariety()
        {
            // Act
            var names = new HashSet<string>();
            for (int i = 0; i < 15; i++)
            {
                names.Add(PlaylistNameGenerator.GenerateNameByTheme(PlaylistTheme.Genre));
            }

            // Assert
            Assert.True(names.Count >= 8, $"Expected at least 8 different genre names, got {names.Count}");
        }

        [Fact]
        public void GenerateNameByTheme_CreativeTheme_ShouldGenerateVariety()
        {
            // Act
            var names = new HashSet<string>();
            for (int i = 0; i < 15; i++)
            {
                names.Add(PlaylistNameGenerator.GenerateNameByTheme(PlaylistTheme.Creative));
            }

            // Assert
            Assert.True(names.Count >= 8, $"Expected at least 8 different creative names, got {names.Count}");
        }

        [Fact]
        public void GenerateNameByTheme_AllThemes_ShouldReturnReasonableLength()
        {
            // Arrange
            var themes = Enum.GetValues<PlaylistTheme>();

            // Act & Assert
            foreach (var theme in themes)
            {
                var result = PlaylistNameGenerator.GenerateNameByTheme(theme);
                Assert.True(result.Length >= 5, $"{theme} name too short: '{result}' (length: {result.Length})");
                Assert.True(result.Length <= 50, $"{theme} name too long: '{result}' (length: {result.Length})");
            }
        }

        #endregion

        #region GenerateMultipleNames Tests

        [Theory]
        [InlineData(1)]
        [InlineData(3)]
        [InlineData(5)]
        [InlineData(10)]
        public void GenerateMultipleNames_ShouldReturnCorrectCount(int count)
        {
            // Act
            var result = PlaylistNameGenerator.GenerateMultipleNames(count);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(count, result.Count);
        }

        [Fact]
        public void GenerateMultipleNames_ShouldReturnUniqueNames()
        {
            // Act
            var result = PlaylistNameGenerator.GenerateMultipleNames(10);

            // Assert
            var uniqueNames = result.Distinct().ToList();
            Assert.Equal(result.Count, uniqueNames.Count);
        }

        [Fact]
        public void GenerateMultipleNames_WithTheme_ShouldReturnCorrectCount()
        {
            // Act
            var result = PlaylistNameGenerator.GenerateMultipleNames(5, PlaylistTheme.Mood);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(5, result.Count);
        }

        [Fact]
        public void GenerateMultipleNames_WithTheme_ShouldReturnUniqueNames()
        {
            // Act
            var result = PlaylistNameGenerator.GenerateMultipleNames(8, PlaylistTheme.Genre);

            // Assert
            var uniqueNames = result.Distinct().ToList();
            Assert.Equal(result.Count, uniqueNames.Count);
        }

        [Fact]
        public void GenerateMultipleNames_AllNamesValid()
        {
            // Act
            var result = PlaylistNameGenerator.GenerateMultipleNames(10);

            // Assert
            foreach (var name in result)
            {
                Assert.NotNull(name);
                Assert.NotEmpty(name);
                Assert.False(string.IsNullOrWhiteSpace(name));
                Assert.True(name.Length >= 5, $"Name too short: '{name}'");
                Assert.True(name.Length <= 50, $"Name too long: '{name}'");
            }
        }

        [Theory]
        [InlineData(PlaylistTheme.Mood)]
        [InlineData(PlaylistTheme.Time)]
        [InlineData(PlaylistTheme.Genre)]
        [InlineData(PlaylistTheme.Creative)]
        public void GenerateMultipleNames_WithTheme_AllNamesValid(PlaylistTheme theme)
        {
            // Act
            var result = PlaylistNameGenerator.GenerateMultipleNames(7, theme);

            // Assert
            foreach (var name in result)
            {
                Assert.NotNull(name);
                Assert.NotEmpty(name);
                Assert.False(string.IsNullOrWhiteSpace(name));
                Assert.True(name.Length >= 5, $"{theme} name too short: '{name}'");
                Assert.True(name.Length <= 50, $"{theme} name too long: '{name}'");
            }
        }

        #endregion

        #region Edge Cases and Error Handling

        [Fact]
        public void GenerateMultipleNames_ZeroCount_ShouldReturnEmptyList()
        {
            // Act
            var result = PlaylistNameGenerator.GenerateMultipleNames(0);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public void GenerateMultipleNames_NegativeCount_ShouldReturnEmptyList()
        {
            // Act
            var result = PlaylistNameGenerator.GenerateMultipleNames(-1);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public void GenerateMultipleNames_LargeCount_ShouldHandleGracefully()
        {
            // Act
            var result = PlaylistNameGenerator.GenerateMultipleNames(100);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Count > 0, "Should generate at least some names");
            Assert.True(result.Count <= 100, "Should not exceed requested count");
            
            // All names should be unique
            var uniqueNames = result.Distinct().ToList();
            Assert.Equal(result.Count, uniqueNames.Count);
        }

        #endregion

        #region Integration Tests

        [Fact]
        public void PlaylistNameGenerator_AllMethods_ShouldWorkTogether()
        {
            // Act
            var randomName = PlaylistNameGenerator.GenerateRandomName();
            var moodName = PlaylistNameGenerator.GenerateNameByTheme(PlaylistTheme.Mood);
            var multipleNames = PlaylistNameGenerator.GenerateMultipleNames(3);

            // Assert
            Assert.NotNull(randomName);
            Assert.NotNull(moodName);
            Assert.NotNull(multipleNames);
            Assert.Equal(3, multipleNames.Count);

            // All names should be different
            var allNames = new List<string> { randomName, moodName };
            allNames.AddRange(multipleNames);
            
            Assert.True(allNames.All(name => !string.IsNullOrWhiteSpace(name)));
        }

        [Fact]
        public void PlaylistNameGenerator_StressTest_ShouldPerformWell()
        {
            // Act & Assert - Should complete without throwing exceptions
            for (int i = 0; i < 100; i++)
            {
                var randomName = PlaylistNameGenerator.GenerateRandomName();
                Assert.NotNull(randomName);
                
                if (i % 10 == 0) // Test themed generation every 10 iterations
                {
                    var theme = (PlaylistTheme)(i % 4);
                    var themedName = PlaylistNameGenerator.GenerateNameByTheme(theme);
                    Assert.NotNull(themedName);
                }
            }
        }

        #endregion
    }
}
