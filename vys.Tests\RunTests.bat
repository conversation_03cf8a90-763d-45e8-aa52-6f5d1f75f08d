@echo off
echo Running Spotify Automation Unit Tests...
echo.

echo Building test project...
dotnet build vys.Tests
if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Running all tests...
dotnet test vys.Tests --verbosity normal --logger "console;verbosity=detailed"

echo.
echo Running specific test categories...

echo.
echo === Playlist Tests ===
dotnet test vys.Tests --filter "FullyQualifiedName~SpotifyPlaylistTests" --verbosity normal

echo.
echo === Search Tests ===
dotnet test vys.Tests --filter "FullyQualifiedName~SpotifySearchTests" --verbosity normal

echo.
echo === Token Tests ===
dotnet test vys.Tests --filter "FullyQualifiedName~SpotifyTokenTests" --verbosity normal

echo.
echo === Navigation Tests ===
dotnet test vys.Tests --filter "FullyQualifiedName~SpotifyNavigationTests" --verbosity normal

echo.
echo === Utility Tests ===
dotnet test vys.Tests --filter "FullyQualifiedName~SpotifyUtilityTests" --verbosity normal

echo.
echo === Playlist Name Generator Tests ===
dotnet test vys.Tests --filter "FullyQualifiedName~PlaylistNameGeneratorTests" --verbosity normal

echo.
echo === Icon Generator Tests ===
dotnet test vys.Tests --filter "FullyQualifiedName~IconGeneratorTests" --verbosity normal

echo.
echo === Dashboard Tests ===
dotnet test vys.Tests --filter "FullyQualifiedName~DashboardNavigationTests" --verbosity normal

echo.
echo === Original Automation Tests ===
dotnet test vys.Tests --filter "FullyQualifiedName~SpotifyAutomationTests" --verbosity normal

echo.
echo Test run completed!
pause
