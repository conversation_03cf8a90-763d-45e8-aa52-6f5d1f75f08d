using System;
using System.Drawing;
using System.IO;
using System.Windows;
using Hardcodet.Wpf.TaskbarNotification;
using WpfApplication = System.Windows.Application;
using vys.Logging;

namespace vys
{
    /// <summary>
    /// Manages system tray functionality and icon status
    /// </summary>
    public class SystemTrayManager : IDisposable
    {
        private TaskbarIcon? _taskbarIcon;
        private bool _disposed = false;

        public SystemTrayManager()
        {
            InitializeSystemTray();
        }

        private void InitializeSystemTray()
        {
            try
            {
                _taskbarIcon = new TaskbarIcon
                {
                    ToolTipText = "VYS Browser - Spotify Status",
                    Visibility = Visibility.Visible
                };

                // Set initial icon (default/green)
                SetStatusIcon(SpotifyLoginStatus.Unknown);

                // Add context menu
                var contextMenu = new System.Windows.Controls.ContextMenu();
                
                var showMenuItem = new System.Windows.Controls.MenuItem
                {
                    Header = "Show VYS Browser"
                };
                showMenuItem.Click += (s, e) => ShowMainWindow();
                
                var exitMenuItem = new System.Windows.Controls.MenuItem
                {
                    Header = "Exit"
                };
                exitMenuItem.Click += (s, e) => ExitApplication();

                contextMenu.Items.Add(showMenuItem);
                contextMenu.Items.Add(new System.Windows.Controls.Separator());
                contextMenu.Items.Add(exitMenuItem);

                _taskbarIcon.ContextMenu = contextMenu;

                // Double-click to show window
                _taskbarIcon.TrayMouseDoubleClick += (s, e) => ShowMainWindow();

                Logger.Info("SystemTrayManager", "System tray initialized successfully");
            }
            catch (Exception ex)
            {
                Logger.Error("SystemTrayManager", ex, "Error initializing system tray");
            }
        }

        /// <summary>
        /// Updates the system tray icon based on Spotify login status
        /// </summary>
        /// <param name="status">Current Spotify login status</param>
        public void SetStatusIcon(SpotifyLoginStatus status)
        {
            if (_taskbarIcon == null) return;

            try
            {
                string iconPath;
                string tooltipText;

                switch (status)
                {
                    case SpotifyLoginStatus.LoggedIn:
                        iconPath = "Resources/app_icon_green.ico";
                        tooltipText = "VYS Browser - Spotify: Logged In";
                        break;
                    case SpotifyLoginStatus.NotLoggedIn:
                        iconPath = "Resources/app_icon_red.ico";
                        tooltipText = "VYS Browser - Spotify: Not Logged In";
                        break;
                    case SpotifyLoginStatus.Error:
                        iconPath = "Resources/app_icon_red.ico";
                        tooltipText = "VYS Browser - Error State";
                        break;
                    default:
                        iconPath = "Resources/app_icon.ico";
                        tooltipText = "VYS Browser - Checking Status...";
                        break;
                }

                // Update icon if file exists
                if (File.Exists(iconPath))
                {
                    _taskbarIcon.Icon = new Icon(iconPath);
                }
                else
                {
                    // Fallback to default icon
                    if (File.Exists("Resources/app_icon.ico"))
                    {
                        _taskbarIcon.Icon = new Icon("Resources/app_icon.ico");
                    }
                }

                _taskbarIcon.ToolTipText = tooltipText;
                Logger.Debug("SystemTrayManager", $"System tray icon updated: {status} - {tooltipText}");
            }
            catch (Exception ex)
            {
                Logger.Error("SystemTrayManager", ex, "Error updating system tray icon");
            }
        }

        private void ShowMainWindow()
        {
            try
            {
                var mainWindow = WpfApplication.Current.MainWindow;
                if (mainWindow != null)
                {
                    Logger.Debug("SystemTrayManager", $"Restoring window from tray - Current state: {mainWindow.WindowState}, Visible: {mainWindow.IsVisible}");

                    // First, ensure the window is shown
                    mainWindow.Show();

                    // Then restore the window state
                    if (mainWindow.WindowState == WindowState.Minimized)
                    {
                        mainWindow.WindowState = WindowState.Normal;
                    }

                    // Force window to be visible and active
                    mainWindow.Visibility = Visibility.Visible;
                    mainWindow.Activate();

                    // Bring to foreground using Windows API if needed
                    try
                    {
                        var handle = new System.Windows.Interop.WindowInteropHelper(mainWindow).Handle;
                        if (handle != IntPtr.Zero)
                        {
                            SetForegroundWindow(handle);
                            ShowWindow(handle, SW_RESTORE);
                        }
                    }
                    catch (Exception apiEx)
                    {
                        Logger.Debug("SystemTrayManager", $"Windows API call failed, using fallback: {apiEx.Message}");
                        // Fallback method
                        mainWindow.Topmost = true;
                        mainWindow.Topmost = false;
                    }

                    mainWindow.Focus();

                    Logger.Debug("SystemTrayManager", $"Window restored - New state: {mainWindow.WindowState}, Visible: {mainWindow.IsVisible}");
                }
                else
                {
                    Logger.Warning("SystemTrayManager", "MainWindow is null, cannot restore from tray");
                }
            }
            catch (Exception ex)
            {
                Logger.Error("SystemTrayManager", ex, "Error showing main window from tray");
            }
        }

        // Windows API declarations for proper window restoration
        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern bool SetForegroundWindow(IntPtr hWnd);

        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

        private const int SW_RESTORE = 9;

        private void ExitApplication()
        {
            try
            {
                Logger.Info("SystemTrayManager", "Exiting application from tray");
                WpfApplication.Current.Shutdown();
            }
            catch (Exception ex)
            {
                Logger.Error("SystemTrayManager", ex, "Error exiting application");
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _taskbarIcon?.Dispose();
                _disposed = true;
            }
        }
    }

    public enum SpotifyLoginStatus
    {
        Unknown,
        LoggedIn,
        NotLoggedIn,
        Error
    }
}
