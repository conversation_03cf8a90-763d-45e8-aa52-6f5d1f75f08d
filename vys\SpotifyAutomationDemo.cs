using System;
using System.Threading.Tasks;
using vys.Logging;

namespace vys
{
    /// <summary>
    /// Demonstrates how to use the Spotify automation system
    /// </summary>
    public class SpotifyAutomationDemo
    {
        private readonly MainWindow _mainWindow;

        public SpotifyAutomationDemo(MainWindow mainWindow)
        {
            _mainWindow = mainWindow ?? throw new ArgumentNullException(nameof(mainWindow));
        }

        /// <summary>
        /// Demonstrates playlist creation and management workflow
        /// </summary>
        /// <param name="playlistName">Name for the test playlist</param>
        public async Task DemoPlaylistManagementAsync(string playlistName)
        {
            try
            {
                Logger.Info("SpotifyAutomationDemo", "Starting playlist management demo...");

                // Step 1: Navigate to dashboard
                Logger.Info("SpotifyAutomationDemo", "Step 1: Navigating to dashboard...");
                var dashboardSuccess = await _mainWindow.SpotifyGoToDashboardAsync();

                if (!dashboardSuccess)
                {
                    Logger.Error("SpotifyAutomationDemo", "Dashboard navigation failed, stopping demo");
                    return;
                }

                await Task.Delay(2000);

                // Step 2: Create a playlist
                Logger.Info("SpotifyAutomationDemo", $"Step 2: Creating playlist '{playlistName}'...");
                var createSuccess = await _mainWindow.SpotifyCreatePlaylistAsync(playlistName);

                if (!createSuccess)
                {
                    Logger.Error("SpotifyAutomationDemo", "Playlist creation failed, stopping demo");
                    return;
                }

                // Wait a moment for playlist to be created
                await Task.Delay(3000);

                Logger.Info("SpotifyAutomationDemo", $"Playlist '{playlistName}' created successfully!");

                // Step 3: Check if playlist exists
                Logger.Info("SpotifyAutomationDemo", $"Step 3: Checking if playlist '{playlistName}' exists...");
                var exists = await _mainWindow.SpotifyPlaylistExistsAsync(playlistName);
                Logger.Info("SpotifyAutomationDemo", $"Playlist exists: {exists}");

                // Step 4: Wait and then demonstrate deletion (optional)
                Logger.Info("SpotifyAutomationDemo", "Waiting 5 seconds before demonstrating deletion...");
                await Task.Delay(5000);

                // Step 5: Delete the playlist
                Logger.Info("SpotifyAutomationDemo", $"Step 5: Deleting playlist '{playlistName}'...");
                var deleteSuccess = await _mainWindow.SpotifyDeletePlaylistAsync(playlistName);

                if (deleteSuccess)
                {
                    Logger.Info("SpotifyAutomationDemo", $"Playlist '{playlistName}' deleted successfully!");

                    // Verify deletion
                    await Task.Delay(2000);
                    var stillExists = await _mainWindow.SpotifyPlaylistExistsAsync(playlistName);
                    Logger.Info("SpotifyAutomationDemo", $"Playlist still exists after deletion: {stillExists}");
                }
                else
                {
                    Logger.Warning("SpotifyAutomationDemo", "Playlist deletion failed");
                }

                Logger.Info("SpotifyAutomationDemo", "Playlist management demo completed!");
            }
            catch (Exception ex)
            {
                Logger.Error("SpotifyAutomationDemo", ex, "Error during playlist management demo");
            }
        }

        /// <summary>
        /// Demonstrates search workflow (without login)
        /// </summary>
        /// <param name="searchTerm">Song or artist to search for</param>
        public async Task DemoSearchAsync(string searchTerm)
        {
            try
            {
                Logger.Info("SpotifyAutomationDemo", "Starting search demo...");

                // Step 1: Search for a song
                Logger.Info("SpotifyAutomationDemo", $"Step 1: Searching for '{searchTerm}'...");
                var searchSuccess = await _mainWindow.SpotifySearchAsync(searchTerm);

                if (!searchSuccess)
                {
                    Logger.Error("SpotifyAutomationDemo", "Search failed, stopping demo");
                    return;
                }

                // Wait for search results
                await Task.Delay(2000);

                Logger.Info("SpotifyAutomationDemo", "Search demo completed successfully!");
            }
            catch (Exception ex)
            {
                Logger.Error("SpotifyAutomationDemo", ex, "Error during search demo");
            }
        }



        /// <summary>
        /// Demonstrates dashboard navigation testing
        /// </summary>
        public async Task DemoDashboardNavigationTestAsync()
        {
            try
            {
                Logger.Info("SpotifyAutomationDemo", "Demonstrating dashboard navigation testing...");

                // Test current page first
                Logger.Info("SpotifyAutomationDemo", "Step 1: Testing current page for dashboard elements...");
                var currentPageTest = await _mainWindow.SpotifyTestDashboardNavigationAsync();
                Logger.Info("SpotifyAutomationDemo", $"Current page test result: {(currentPageTest ? "PASSED" : "FAILED")}");

                // Run comprehensive test (navigate + verify)
                Logger.Info("SpotifyAutomationDemo", "Step 2: Running comprehensive dashboard navigation test...");
                var comprehensiveTest = await _mainWindow.SpotifyTestGoToDashboardAsync();

                if (comprehensiveTest)
                {
                    Logger.Info("SpotifyAutomationDemo", "✅ COMPREHENSIVE TEST PASSED - Dashboard navigation successful and verified");
                }
                else
                {
                    Logger.Warning("SpotifyAutomationDemo", "❌ COMPREHENSIVE TEST FAILED - Dashboard navigation or verification failed");
                }

                // Test again after navigation
                await Task.Delay(2000);
                Logger.Info("SpotifyAutomationDemo", "Step 3: Final verification test...");
                var finalTest = await _mainWindow.SpotifyTestDashboardNavigationAsync();
                Logger.Info("SpotifyAutomationDemo", $"Final verification result: {(finalTest ? "PASSED" : "FAILED")}");

                Logger.Info("SpotifyAutomationDemo", "Dashboard navigation testing demo completed!");
            }
            catch (Exception ex)
            {
                Logger.Error("SpotifyAutomationDemo", ex, "Error during dashboard navigation testing demo");
            }
        }

        /// <summary>
        /// Demonstrates custom JavaScript execution
        /// </summary>
        public async Task DemoCustomScriptAsync()
        {
            try
            {
                Logger.Info("SpotifyAutomationDemo", "Demonstrating custom script execution...");

                // Example 1: Get page title
                var titleScript = "document.title";
                var title = await _mainWindow.ExecuteCustomScriptAsync(titleScript);
                Logger.Info("SpotifyAutomationDemo", $"Page title: {title}");

                // Example 2: Check if user is logged in
                var loginCheckScript = @"
                (function() {
                    const userWidget = document.querySelector('[data-testid=""user-widget-link""]');
                    const topBarWidget = document.querySelector('[data-testid=""top-bar-user-widget""]');
                    return userWidget !== null || topBarWidget !== null;
                })();";
                
                var isLoggedIn = await _mainWindow.ExecuteCustomScriptAsync(loginCheckScript);
                Logger.Info("SpotifyAutomationDemo", $"User appears logged in: {isLoggedIn}");

                // Example 3: Get all visible buttons (for debugging)
                var buttonsScript = @"
                (function() {
                    const buttons = Array.from(document.querySelectorAll('button:not([style*=""display: none""])'));
                    return buttons.length;
                })();";
                
                var buttonCount = await _mainWindow.ExecuteCustomScriptAsync(buttonsScript);
                Logger.Info("SpotifyAutomationDemo", $"Visible buttons on page: {buttonCount}");

                Logger.Info("SpotifyAutomationDemo", "Custom script demo completed!");
            }
            catch (Exception ex)
            {
                Logger.Error("SpotifyAutomationDemo", ex, "Error during custom script demo");
            }
        }

        /// <summary>
        /// Demonstrates searching multiple songs
        /// </summary>
        /// <param name="searchTerms">Array of songs/artists to search for</param>
        public async Task DemoMultipleSongsAsync(string[] searchTerms)
        {
            try
            {
                Logger.Info("SpotifyAutomationDemo", $"Searching for {searchTerms.Length} songs in sequence...");

                foreach (var searchTerm in searchTerms)
                {
                    Logger.Info("SpotifyAutomationDemo", $"Searching for: {searchTerm}");

                    // Search
                    var searchSuccess = await _mainWindow.SpotifySearchAsync(searchTerm);
                    if (!searchSuccess)
                    {
                        Logger.Warning("SpotifyAutomationDemo", $"Failed to search for: {searchTerm}");
                        continue;
                    }

                    await Task.Delay(2000);
                    Logger.Info("SpotifyAutomationDemo", $"Search completed for: {searchTerm}");

                    // Wait before next search
                    await Task.Delay(2000);
                }

                Logger.Info("SpotifyAutomationDemo", "Multiple songs demo completed!");
            }
            catch (Exception ex)
            {
                Logger.Error("SpotifyAutomationDemo", ex, "Error during multiple songs demo");
            }
        }

        /// <summary>
        /// Demonstrates playing a playlist
        /// </summary>
        /// <param name="playlistName">Name of the playlist to play</param>
        public async Task DemoPlayPlaylistAsync(string playlistName)
        {
            try
            {
                Logger.Info("SpotifyAutomationDemo", $"Starting play playlist demo for: {playlistName}");

                // Play the playlist
                Logger.Info("SpotifyAutomationDemo", $"Playing playlist: {playlistName}");
                var playSuccess = await _mainWindow.SpotifyPlayPlaylistAsync(playlistName);

                if (playSuccess)
                {
                    Logger.Info("SpotifyAutomationDemo", $"Successfully started playing playlist: {playlistName}");

                    // Wait a moment and get current song info
                    await Task.Delay(3000);
                    var currentSong = await _mainWindow.SpotifyGetCurrentSongAsync();
                    if (!string.IsNullOrEmpty(currentSong))
                    {
                        Logger.Info("SpotifyAutomationDemo", $"Now playing from playlist: {currentSong}");
                    }
                }
                else
                {
                    Logger.Warning("SpotifyAutomationDemo", $"Failed to play playlist: {playlistName}");
                }

                Logger.Info("SpotifyAutomationDemo", "Play playlist demo completed!");
            }
            catch (Exception ex)
            {
                Logger.Error("SpotifyAutomationDemo", ex, $"Error during play playlist demo for '{playlistName}'");
            }
        }

        /// <summary>
        /// Demonstrates listing all songs in a playlist
        /// </summary>
        /// <param name="playlistName">Name of the playlist to list songs from</param>
        public async Task DemoListPlaylistSongsAsync(string playlistName)
        {
            try
            {
                Logger.Info("SpotifyAutomationDemo", $"Starting list playlist songs demo for: {playlistName}");

                // List songs in the playlist
                Logger.Info("SpotifyAutomationDemo", $"Listing songs in playlist: {playlistName}");
                var songs = await _mainWindow.SpotifyListPlaylistSongsAsync(playlistName);

                if (songs.Count > 0)
                {
                    Logger.Info("SpotifyAutomationDemo", $"Successfully retrieved {songs.Count} songs from playlist '{playlistName}':");

                    for (int i = 0; i < songs.Count; i++)
                    {
                        Logger.Info("SpotifyAutomationDemo", $"  {i + 1}. {songs[i]}");
                    }
                }
                else
                {
                    Logger.Warning("SpotifyAutomationDemo", $"No songs found in playlist: {playlistName}");
                }

                Logger.Info("SpotifyAutomationDemo", "List playlist songs demo completed!");
            }
            catch (Exception ex)
            {
                Logger.Error("SpotifyAutomationDemo", ex, $"Error during list playlist songs demo for '{playlistName}'");
            }
        }

        /// <summary>
        /// Demonstrates adding songs to a playlist workflow
        /// </summary>
        /// <param name="playlistName">Name of the playlist to create and add songs to</param>
        public async Task DemoAddSongsToPlaylistAsync(string playlistName)
        {
            try
            {
                Logger.Info("SpotifyAutomationDemo", "Starting add songs to playlist demo...");

                // Step 1: Navigate to dashboard
                Logger.Info("SpotifyAutomationDemo", "Step 1: Navigating to dashboard...");
                var dashboardSuccess = await _mainWindow.SpotifyGoToDashboardAsync();

                if (!dashboardSuccess)
                {
                    Logger.Error("SpotifyAutomationDemo", "Dashboard navigation failed, stopping demo");
                    return;
                }

                await Task.Delay(2000);

                // Step 2: Check if playlist exists, create if not
                Logger.Info("SpotifyAutomationDemo", $"Step 2: Checking if playlist '{playlistName}' exists...");
                var playlistExists = await _mainWindow.SpotifyPlaylistExistsAsync(playlistName);

                string? playlistId = null;

                if (!playlistExists)
                {
                    Logger.Info("SpotifyAutomationDemo", $"Playlist doesn't exist, creating '{playlistName}'...");
                    var createSuccess = await _mainWindow.SpotifyCreatePlaylistAsync(playlistName);

                    if (!createSuccess)
                    {
                        Logger.Error("SpotifyAutomationDemo", "Playlist creation failed, stopping demo");
                        return;
                    }

                    await Task.Delay(3000); // Wait for playlist creation to complete

                    // Get the playlist ID from the current URL (should be on the new playlist page)
                    playlistId = await _mainWindow.SpotifyGetCurrentPlaylistIdAsync();
                }
                else
                {
                    Logger.Info("SpotifyAutomationDemo", $"Playlist '{playlistName}' already exists");
                    // Navigate to the existing playlist to get its ID
                    // For demo purposes, we'll search for it and navigate to it
                    // In a real scenario, you might store playlist IDs or have another way to get them
                }

                if (string.IsNullOrEmpty(playlistId))
                {
                    Logger.Warning("SpotifyAutomationDemo", "Could not get playlist ID, trying alternative approach");

                    // Alternative: Navigate to the playlist by searching for it
                    await _mainWindow.SpotifyGoToDashboardAsync();
                    await Task.Delay(2000);

                    // Try to find and click on the playlist
                    // This is a simplified approach - in practice you might need more robust playlist navigation
                    playlistId = await _mainWindow.SpotifyGetCurrentPlaylistIdAsync();
                }

                if (string.IsNullOrEmpty(playlistId))
                {
                    Logger.Error("SpotifyAutomationDemo", "Could not determine playlist ID, stopping demo");
                    return;
                }

                Logger.Info("SpotifyAutomationDemo", $"Using playlist ID: {playlistId}");

                // Step 3: Add some songs to the playlist
                var songsToAdd = new[]
                {
                    "Bohemian Rhapsody Queen",
                    "Hotel California Eagles",
                    "Stairway to Heaven Led Zeppelin"
                };

                foreach (var song in songsToAdd)
                {
                    Logger.Info("SpotifyAutomationDemo", $"Step 3.{Array.IndexOf(songsToAdd, song) + 1}: Adding '{song}' to playlist...");

                    var addSuccess = await _mainWindow.SpotifySearchAndAddSongToPlaylistAsync(song, playlistId);

                    if (addSuccess)
                    {
                        Logger.Info("SpotifyAutomationDemo", $"✅ Successfully added '{song}' to playlist");
                    }
                    else
                    {
                        Logger.Warning("SpotifyAutomationDemo", $"❌ Failed to add '{song}' to playlist");
                    }

                    // Wait between additions to avoid overwhelming the API
                    await Task.Delay(3000);
                }

                Logger.Info("SpotifyAutomationDemo", "Add songs to playlist demo completed!");
            }
            catch (Exception ex)
            {
                Logger.Error("SpotifyAutomationDemo", ex, "Error during add songs to playlist demo");
            }
        }
    }
}
