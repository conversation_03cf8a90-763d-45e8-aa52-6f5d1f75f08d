using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Moq;
using Xunit;
using vys.Interfaces;

namespace vys.Tests
{
    public class SpotifyPlaylistListingTests
    {
        private readonly Mock<IWebView2Wrapper> _mockWebView;
        private readonly SpotifyAutomation _spotifyAutomation;

        public SpotifyPlaylistListingTests()
        {
            _mockWebView = new Mock<IWebView2Wrapper>();
            _spotifyAutomation = new SpotifyAutomation(_mockWebView.Object);
        }

        [Fact]
        public async Task ListAllPlaylistsAsync_WithValidPlaylists_ShouldReturnPlaylistNames()
        {
            // Arrange
            var mockResponse = @"{
                ""success"": true,
                ""count"": 2,
                ""names"": [
                    ""My Rock Playlist"",
                    ""Jazz Collection""
                ]
            }";

            _mockWebView.SetupSequence(w => w.ExecuteScriptAsync(It.IsAny<string>()))
                .ReturnsAsync("{}") // SetupTokenInterception call
                .ReturnsAsync("LIBRARY_ALREADY_EXPANDED") // Library expansion
                .ReturnsAsync(mockResponse); // Playlist extraction

            // Act
            var result = await _spotifyAutomation.ListAllPlaylistsAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.Contains("My Rock Playlist", result);
            Assert.Contains("Jazz Collection", result);
        }

        [Fact]
        public async Task ListAllPlaylistsAsync_WithEmptyPlaylistResponse_ShouldReturnEmptyList()
        {
            // Arrange
            var mockResponse = @"{
                ""success"": true,
                ""count"": 0,
                ""names"": []
            }";

            _mockWebView.SetupSequence(w => w.ExecuteScriptAsync(It.IsAny<string>()))
                .ReturnsAsync("{}") // SetupTokenInterception call
                .ReturnsAsync("LIBRARY_ALREADY_EXPANDED")
                .ReturnsAsync(mockResponse);

            // Act
            var result = await _spotifyAutomation.ListAllPlaylistsAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task ListAllPlaylistsAsync_WithFailedResponse_ShouldReturnEmptyList()
        {
            // Arrange
            var mockResponse = @"{
                ""success"": false,
                ""error"": ""Failed to extract playlists"",
                ""count"": 0,
                ""names"": []
            }";

            _mockWebView.SetupSequence(w => w.ExecuteScriptAsync(It.IsAny<string>()))
                .ReturnsAsync("{}") // SetupTokenInterception call
                .ReturnsAsync("LIBRARY_ALREADY_EXPANDED")
                .ReturnsAsync(mockResponse);

            // Act
            var result = await _spotifyAutomation.ListAllPlaylistsAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task ListAllPlaylistsAsync_WithInvalidJson_ShouldReturnEmptyList()
        {
            // Arrange
            var invalidJson = "{ invalid json }";

            _mockWebView.SetupSequence(w => w.ExecuteScriptAsync(It.IsAny<string>()))
                .ReturnsAsync("{}") // SetupTokenInterception call
                .ReturnsAsync("LIBRARY_ALREADY_EXPANDED")
                .ReturnsAsync(invalidJson);

            // Act
            var result = await _spotifyAutomation.ListAllPlaylistsAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task ListAllPlaylistsAsync_WithNullResponse_ShouldReturnEmptyList()
        {
            // Arrange
            _mockWebView.SetupSequence(w => w.ExecuteScriptAsync(It.IsAny<string>()))
                .ReturnsAsync("{}") // SetupTokenInterception call
                .ReturnsAsync("LIBRARY_ALREADY_EXPANDED")
                .ReturnsAsync((string)null!);

            // Act
            var result = await _spotifyAutomation.ListAllPlaylistsAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task ListAllPlaylistsAsync_WithEmptyResponse_ShouldReturnEmptyList()
        {
            // Arrange
            _mockWebView.SetupSequence(w => w.ExecuteScriptAsync(It.IsAny<string>()))
                .ReturnsAsync("{}") // SetupTokenInterception call
                .ReturnsAsync("LIBRARY_ALREADY_EXPANDED")
                .ReturnsAsync("");

            // Act
            var result = await _spotifyAutomation.ListAllPlaylistsAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task ListAllPlaylistsAsync_WithLibraryExpansionError_ShouldStillAttemptExtraction()
        {
            // Arrange
            var mockResponse = @"{
                ""success"": true,
                ""count"": 1,
                ""names"": [
                    ""Test Playlist""
                ]
            }";

            _mockWebView.SetupSequence(w => w.ExecuteScriptAsync(It.IsAny<string>()))
                .ReturnsAsync("{}") // SetupTokenInterception call
                .ReturnsAsync("ERROR: Library expansion failed")
                .ReturnsAsync(mockResponse);

            // Act
            var result = await _spotifyAutomation.ListAllPlaylistsAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result); // Should return empty due to library error
        }

        [Fact]
        public async Task ListAllPlaylistsAsync_WithScriptException_ShouldReturnEmptyList()
        {
            // Arrange
            _mockWebView.Setup(w => w.ExecuteScriptAsync(It.IsAny<string>()))
                .ThrowsAsync(new Exception("Script execution failed"));

            // Act
            var result = await _spotifyAutomation.ListAllPlaylistsAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task ListAllPlaylistsAsync_WithPartialPlaylistData_ShouldHandleGracefully()
        {
            // Arrange
            var mockResponse = @"{
                ""success"": true,
                ""count"": 2,
                ""names"": [
                    ""Complete Playlist"",
                    ""Minimal Playlist""
                ]
            }";

            _mockWebView.SetupSequence(w => w.ExecuteScriptAsync(It.IsAny<string>()))
                .ReturnsAsync("{}") // SetupTokenInterception call
                .ReturnsAsync("LIBRARY_ALREADY_EXPANDED")
                .ReturnsAsync(mockResponse);

            // Act
            var result = await _spotifyAutomation.ListAllPlaylistsAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.Contains("Complete Playlist", result);
            Assert.Contains("Minimal Playlist", result);
        }

        [Theory]
        [InlineData("LIBRARY_EXPANDED")]
        [InlineData("LIBRARY_ALREADY_EXPANDED")]
        [InlineData("LIBRARY_BUTTON_NOT_FOUND")]
        public async Task ListAllPlaylistsAsync_WithDifferentLibraryStates_ShouldProceedToExtraction(string libraryState)
        {
            // Arrange
            var mockResponse = @"{
                ""success"": true,
                ""count"": 1,
                ""names"": [
                    ""Test Playlist""
                ]
            }";

            _mockWebView.SetupSequence(w => w.ExecuteScriptAsync(It.IsAny<string>()))
                .ReturnsAsync("{}") // SetupTokenInterception call
                .ReturnsAsync(libraryState)
                .ReturnsAsync(mockResponse);

            // Act
            var result = await _spotifyAutomation.ListAllPlaylistsAsync();

            // Assert
            Assert.NotNull(result);
            if (libraryState.Contains("ERROR"))
            {
                Assert.Empty(result);
            }
            else
            {
                Assert.Single(result);
                Assert.Equal("Test Playlist", result[0]);
            }
        }
    }
}
