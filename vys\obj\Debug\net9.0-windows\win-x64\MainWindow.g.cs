﻿#pragma checksum "..\..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "64FA12EA3CBA9FA90410CE3304AC5423A92A1C0B"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using Microsoft.Web.WebView2.Wpf;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using vys;


namespace vys {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 32 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PlaylistNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 36 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CreatePlaylistButton;
        
        #line default
        #line hidden
        
        
        #line 39 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeletePlaylistButton;
        
        #line default
        #line hidden
        
        
        #line 43 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SearchButton;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DashboardButton;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SongNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TargetPlaylistTextBox;
        
        #line default
        #line hidden
        
        
        #line 67 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddSongButton;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox RemoveSongNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox RemoveFromPlaylistTextBox;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RemoveSongButton;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox QuickPlayPlaylistTextBox;
        
        #line default
        #line hidden
        
        
        #line 94 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button QuickPlayButton;
        
        #line default
        #line hidden
        
        
        #line 100 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ListSongsPlaylistTextBox;
        
        #line default
        #line hidden
        
        
        #line 105 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ListSongsButton;
        
        #line default
        #line hidden
        
        
        #line 111 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ShuffleButton;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RepeatButton;
        
        #line default
        #line hidden
        
        
        #line 121 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ListPlaylistsButton;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MuteButton;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DevToolsButton;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ColumnDefinition DevToolsColumn;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Microsoft.Web.WebView2.Wpf.WebView2 Browser;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GridSplitter DevToolsSplitter;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid DevToolsPanel;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseDevToolsButton;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Microsoft.Web.WebView2.Wpf.WebView2 DevToolsWebView;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/vys;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.PlaylistNameTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 35 "..\..\..\..\MainWindow.xaml"
            this.PlaylistNameTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.PlaylistNameTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 35 "..\..\..\..\MainWindow.xaml"
            this.PlaylistNameTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.PlaylistNameTextBox_LostFocus);
            
            #line default
            #line hidden
            return;
            case 2:
            this.CreatePlaylistButton = ((System.Windows.Controls.Button)(target));
            
            #line 37 "..\..\..\..\MainWindow.xaml"
            this.CreatePlaylistButton.Click += new System.Windows.RoutedEventHandler(this.CreatePlaylistButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.DeletePlaylistButton = ((System.Windows.Controls.Button)(target));
            
            #line 40 "..\..\..\..\MainWindow.xaml"
            this.DeletePlaylistButton.Click += new System.Windows.RoutedEventHandler(this.DeletePlaylistButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 46 "..\..\..\..\MainWindow.xaml"
            this.SearchTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.SearchTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 46 "..\..\..\..\MainWindow.xaml"
            this.SearchTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.SearchTextBox_LostFocus);
            
            #line default
            #line hidden
            return;
            case 5:
            this.SearchButton = ((System.Windows.Controls.Button)(target));
            
            #line 48 "..\..\..\..\MainWindow.xaml"
            this.SearchButton.Click += new System.Windows.RoutedEventHandler(this.SearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.DashboardButton = ((System.Windows.Controls.Button)(target));
            
            #line 52 "..\..\..\..\MainWindow.xaml"
            this.DashboardButton.Click += new System.Windows.RoutedEventHandler(this.DashboardButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.SongNameTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 62 "..\..\..\..\MainWindow.xaml"
            this.SongNameTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.SongNameTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 62 "..\..\..\..\MainWindow.xaml"
            this.SongNameTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.SongNameTextBox_LostFocus);
            
            #line default
            #line hidden
            return;
            case 8:
            this.TargetPlaylistTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 66 "..\..\..\..\MainWindow.xaml"
            this.TargetPlaylistTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.TargetPlaylistTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 66 "..\..\..\..\MainWindow.xaml"
            this.TargetPlaylistTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.TargetPlaylistTextBox_LostFocus);
            
            #line default
            #line hidden
            return;
            case 9:
            this.AddSongButton = ((System.Windows.Controls.Button)(target));
            
            #line 68 "..\..\..\..\MainWindow.xaml"
            this.AddSongButton.Click += new System.Windows.RoutedEventHandler(this.AddSongButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.RemoveSongNameTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 77 "..\..\..\..\MainWindow.xaml"
            this.RemoveSongNameTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.RemoveSongNameTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 77 "..\..\..\..\MainWindow.xaml"
            this.RemoveSongNameTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.RemoveSongNameTextBox_LostFocus);
            
            #line default
            #line hidden
            return;
            case 11:
            this.RemoveFromPlaylistTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 81 "..\..\..\..\MainWindow.xaml"
            this.RemoveFromPlaylistTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.RemoveFromPlaylistTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 81 "..\..\..\..\MainWindow.xaml"
            this.RemoveFromPlaylistTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.RemoveFromPlaylistTextBox_LostFocus);
            
            #line default
            #line hidden
            return;
            case 12:
            this.RemoveSongButton = ((System.Windows.Controls.Button)(target));
            
            #line 83 "..\..\..\..\MainWindow.xaml"
            this.RemoveSongButton.Click += new System.Windows.RoutedEventHandler(this.RemoveSongButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.QuickPlayPlaylistTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 93 "..\..\..\..\MainWindow.xaml"
            this.QuickPlayPlaylistTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.QuickPlayPlaylistTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 93 "..\..\..\..\MainWindow.xaml"
            this.QuickPlayPlaylistTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.QuickPlayPlaylistTextBox_LostFocus);
            
            #line default
            #line hidden
            return;
            case 14:
            this.QuickPlayButton = ((System.Windows.Controls.Button)(target));
            
            #line 95 "..\..\..\..\MainWindow.xaml"
            this.QuickPlayButton.Click += new System.Windows.RoutedEventHandler(this.QuickPlayButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.ListSongsPlaylistTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 104 "..\..\..\..\MainWindow.xaml"
            this.ListSongsPlaylistTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.ListSongsPlaylistTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 104 "..\..\..\..\MainWindow.xaml"
            this.ListSongsPlaylistTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.ListSongsPlaylistTextBox_LostFocus);
            
            #line default
            #line hidden
            return;
            case 16:
            this.ListSongsButton = ((System.Windows.Controls.Button)(target));
            
            #line 106 "..\..\..\..\MainWindow.xaml"
            this.ListSongsButton.Click += new System.Windows.RoutedEventHandler(this.ListSongsButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.ShuffleButton = ((System.Windows.Controls.Button)(target));
            
            #line 112 "..\..\..\..\MainWindow.xaml"
            this.ShuffleButton.Click += new System.Windows.RoutedEventHandler(this.ShuffleButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.RepeatButton = ((System.Windows.Controls.Button)(target));
            
            #line 116 "..\..\..\..\MainWindow.xaml"
            this.RepeatButton.Click += new System.Windows.RoutedEventHandler(this.RepeatButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.ListPlaylistsButton = ((System.Windows.Controls.Button)(target));
            
            #line 122 "..\..\..\..\MainWindow.xaml"
            this.ListPlaylistsButton.Click += new System.Windows.RoutedEventHandler(this.ListPlaylistsButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.MuteButton = ((System.Windows.Controls.Button)(target));
            
            #line 130 "..\..\..\..\MainWindow.xaml"
            this.MuteButton.Click += new System.Windows.RoutedEventHandler(this.MuteButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.DevToolsButton = ((System.Windows.Controls.Button)(target));
            
            #line 133 "..\..\..\..\MainWindow.xaml"
            this.DevToolsButton.Click += new System.Windows.RoutedEventHandler(this.DevToolsButton_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.DevToolsColumn = ((System.Windows.Controls.ColumnDefinition)(target));
            return;
            case 23:
            this.Browser = ((Microsoft.Web.WebView2.Wpf.WebView2)(target));
            return;
            case 24:
            this.DevToolsSplitter = ((System.Windows.Controls.GridSplitter)(target));
            return;
            case 25:
            this.DevToolsPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 26:
            this.CloseDevToolsButton = ((System.Windows.Controls.Button)(target));
            
            #line 171 "..\..\..\..\MainWindow.xaml"
            this.CloseDevToolsButton.Click += new System.Windows.RoutedEventHandler(this.CloseDevToolsButton_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            this.DevToolsWebView = ((Microsoft.Web.WebView2.Wpf.WebView2)(target));
            return;
            case 28:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

