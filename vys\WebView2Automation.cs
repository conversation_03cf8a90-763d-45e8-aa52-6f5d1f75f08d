using System;
using System.Threading.Tasks;
using Microsoft.Web.WebView2.Core;
using vys.Logging;
using System.Text.Json;

namespace vys
{
    /// <summary>
    /// Provides human-like automation capabilities for WebView2 interactions
    /// </summary>
    public class WebView2Automation
    {
        private readonly CoreWebView2 _webView;
        private readonly Random _random;

        public WebView2Automation(CoreWebView2 webView)
        {
            _webView = webView ?? throw new ArgumentNullException(nameof(webView));
            _random = new Random();
        }

        /// <summary>
        /// Clicks an element by selector with human-like behavior
        /// </summary>
        /// <param name="selector">CSS selector for the element</param>
        /// <param name="humanDelay">Add human-like delay (default: true)</param>
        /// <returns>True if click was successful</returns>
        public async Task<bool> ClickElementAsync(string selector, bool humanDelay = true)
        {
            try
            {
                Logger.Debug("WebView2Automation", $"Attempting to click element: {selector}");

                if (humanDelay)
                {
                    await HumanDelay(200, 800);
                }

                var script = $@"
                (function() {{
                    const element = document.querySelector('{selector}');
                    if (!element) {{
                        return JSON.stringify({{ success: false, error: 'Element not found' }});
                    }}
                    
                    // Scroll element into view if needed
                    element.scrollIntoView({{ behavior: 'smooth', block: 'center' }});
                    
                    // Add human-like mouse movement simulation
                    const rect = element.getBoundingClientRect();
                    const x = rect.left + rect.width / 2 + (Math.random() - 0.5) * 10;
                    const y = rect.top + rect.height / 2 + (Math.random() - 0.5) * 10;
                    
                    // Simulate mouse events
                    const mouseOverEvent = new MouseEvent('mouseover', {{ bubbles: true, clientX: x, clientY: y }});
                    const mouseDownEvent = new MouseEvent('mousedown', {{ bubbles: true, clientX: x, clientY: y }});
                    const mouseUpEvent = new MouseEvent('mouseup', {{ bubbles: true, clientX: x, clientY: y }});
                    const clickEvent = new MouseEvent('click', {{ bubbles: true, clientX: x, clientY: y }});
                    
                    element.dispatchEvent(mouseOverEvent);
                    setTimeout(() => {{
                        element.dispatchEvent(mouseDownEvent);
                        setTimeout(() => {{
                            element.dispatchEvent(mouseUpEvent);
                            element.dispatchEvent(clickEvent);
                        }}, {_random.Next(50, 150)});
                    }}, {_random.Next(100, 300)});
                    
                    return JSON.stringify({{ success: true, elementText: element.textContent || element.value || '' }});
                }})();";

                var result = await _webView.ExecuteScriptAsync(script);
                var response = JsonSerializer.Deserialize<JsonElement>(result);
                
                if (response.TryGetProperty("success", out var success) && success.GetBoolean())
                {
                    Logger.Info("WebView2Automation", $"Successfully clicked element: {selector}");
                    return true;
                }
                else
                {
                    var error = response.TryGetProperty("error", out var errorProp) ? errorProp.GetString() : "Unknown error";
                    Logger.Warning("WebView2Automation", $"Failed to click element {selector}: {error}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logger.Error("WebView2Automation", ex, $"Error clicking element: {selector}");
                return false;
            }
        }

        /// <summary>
        /// Types text into an input field with human-like typing patterns
        /// </summary>
        /// <param name="selector">CSS selector for the input element</param>
        /// <param name="text">Text to type</param>
        /// <param name="clearFirst">Clear the field before typing (default: true)</param>
        /// <returns>True if typing was successful</returns>
        public async Task<bool> TypeTextAsync(string selector, string text, bool clearFirst = true)
        {
            try
            {
                Logger.Debug("WebView2Automation", $"Typing text into element: {selector}");

                var script = $@"
                (function() {{
                    const element = document.querySelector('{selector}');
                    if (!element) {{
                        return JSON.stringify({{ success: false, error: 'Element not found' }});
                    }}
                    
                    // Focus the element
                    element.focus();
                    element.scrollIntoView({{ behavior: 'smooth', block: 'center' }});
                    
                    {(clearFirst ? "element.value = '';" : "")}
                    
                    // Simulate human typing with realistic delays
                    const textToType = `{text.Replace("`", "\\`").Replace("\\", "\\\\")}`;
                    let currentIndex = 0;
                    
                    function typeNextChar() {{
                        if (currentIndex < textToType.length) {{
                            const char = textToType[currentIndex];
                            
                            // Simulate keydown, keypress, input, keyup events
                            const keyCode = char.charCodeAt(0);
                            
                            const keydownEvent = new KeyboardEvent('keydown', {{ 
                                key: char, 
                                code: 'Key' + char.toUpperCase(), 
                                keyCode: keyCode,
                                bubbles: true 
                            }});
                            
                            const keypressEvent = new KeyboardEvent('keypress', {{ 
                                key: char, 
                                code: 'Key' + char.toUpperCase(), 
                                keyCode: keyCode,
                                bubbles: true 
                            }});
                            
                            const inputEvent = new InputEvent('input', {{ 
                                data: char,
                                bubbles: true 
                            }});
                            
                            const keyupEvent = new KeyboardEvent('keyup', {{ 
                                key: char, 
                                code: 'Key' + char.toUpperCase(), 
                                keyCode: keyCode,
                                bubbles: true 
                            }});
                            
                            element.dispatchEvent(keydownEvent);
                            element.dispatchEvent(keypressEvent);
                            
                            element.value += char;
                            element.dispatchEvent(inputEvent);
                            element.dispatchEvent(keyupEvent);
                            
                            currentIndex++;
                            
                            // Human-like typing speed (50-200ms between characters)
                            const delay = Math.random() * 150 + 50;
                            setTimeout(typeNextChar, delay);
                        }}
                    }}
                    
                    typeNextChar();
                    
                    return JSON.stringify({{ success: true }});
                }})();";

                var result = await _webView.ExecuteScriptAsync(script);
                var response = JsonSerializer.Deserialize<JsonElement>(result);
                
                if (response.TryGetProperty("success", out var success) && success.GetBoolean())
                {
                    Logger.Info("WebView2Automation", $"Successfully typed text into element: {selector}");
                    
                    // Wait for typing to complete
                    await Task.Delay(text.Length * 100 + 500);
                    return true;
                }
                else
                {
                    var error = response.TryGetProperty("error", out var errorProp) ? errorProp.GetString() : "Unknown error";
                    Logger.Warning("WebView2Automation", $"Failed to type into element {selector}: {error}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logger.Error("WebView2Automation", ex, $"Error typing into element: {selector}");
                return false;
            }
        }

        /// <summary>
        /// Waits for an element to appear on the page
        /// </summary>
        /// <param name="selector">CSS selector for the element</param>
        /// <param name="timeoutMs">Timeout in milliseconds (default: 10000)</param>
        /// <returns>True if element was found within timeout</returns>
        public async Task<bool> WaitForElementAsync(string selector, int timeoutMs = 10000)
        {
            try
            {
                Logger.Debug("WebView2Automation", $"Waiting for element: {selector}");

                var startTime = DateTime.Now;
                while ((DateTime.Now - startTime).TotalMilliseconds < timeoutMs)
                {
                    var script = $@"
                    (function() {{
                        const element = document.querySelector('{selector}');
                        return element !== null;
                    }})();";

                    var result = await _webView.ExecuteScriptAsync(script);

                    // Handle the result safely - WebView2 can return "null", "true", "false"
                    if (result != null && (result.Equals("true", StringComparison.OrdinalIgnoreCase) || result == "True"))
                    {
                        Logger.Info("WebView2Automation", $"Element found: {selector}");
                        return true;
                    }

                    await Task.Delay(500);
                }

                Logger.Warning("WebView2Automation", $"Element not found within timeout: {selector}");
                return false;
            }
            catch (Exception ex)
            {
                Logger.Error("WebView2Automation", ex, $"Error waiting for element: {selector}");
                return false;
            }
        }

        /// <summary>
        /// Gets text content from an element
        /// </summary>
        /// <param name="selector">CSS selector for the element</param>
        /// <returns>Text content or null if element not found</returns>
        public async Task<string?> GetElementTextAsync(string selector)
        {
            try
            {
                var script = $@"
                (function() {{
                    const element = document.querySelector('{selector}');
                    return element ? element.textContent || element.value || '' : null;
                }})();";

                var result = await _webView.ExecuteScriptAsync(script);
                return result?.Trim('"');
            }
            catch (Exception ex)
            {
                Logger.Error("WebView2Automation", ex, $"Error getting text from element: {selector}");
                return null;
            }
        }

        /// <summary>
        /// Scrolls the page to bring an element into view
        /// </summary>
        /// <param name="selector">CSS selector for the element</param>
        /// <returns>True if scroll was successful</returns>
        public async Task<bool> ScrollToElementAsync(string selector)
        {
            try
            {
                var script = $@"
                (function() {{
                    const element = document.querySelector('{selector}');
                    if (element) {{
                        element.scrollIntoView({{ behavior: 'smooth', block: 'center' }});
                        return true;
                    }}
                    return false;
                }})();";

                var result = await _webView.ExecuteScriptAsync(script);

                // Handle the result safely - WebView2 can return "null", "true", "false"
                return result != null && (result.Equals("true", StringComparison.OrdinalIgnoreCase) || result == "True");
            }
            catch (Exception ex)
            {
                Logger.Error("WebView2Automation", ex, $"Error scrolling to element: {selector}");
                return false;
            }
        }

        /// <summary>
        /// Adds human-like delay between actions
        /// </summary>
        /// <param name="minMs">Minimum delay in milliseconds</param>
        /// <param name="maxMs">Maximum delay in milliseconds</param>
        private async Task HumanDelay(int minMs = 500, int maxMs = 2000)
        {
            var delay = _random.Next(minMs, maxMs);
            await Task.Delay(delay);
        }
    }
}
