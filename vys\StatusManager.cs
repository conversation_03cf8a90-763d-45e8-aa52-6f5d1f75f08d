using System;
using System.IO;
using System.Windows;
using System.Windows.Media.Imaging;
using WpfApplication = System.Windows.Application;
using vys.Logging;

namespace vys
{
    public class StatusManager : IDisposable
    {
        private readonly SystemTrayManager _systemTrayManager;
        private SpotifyLoginStatus _currentStatus = SpotifyLoginStatus.Unknown;
        private bool _disposed = false;

        public StatusManager()
        {
            _systemTrayManager = new SystemTrayManager();
        }

        public event EventHandler<ApplicationStatusChangedEventArgs>? StatusChanged;

        public SpotifyLoginStatus CurrentStatus => _currentStatus;

        public void UpdateSpotifyStatus(SpotifyLoginStatus spotifyStatus)
        {
            try
            {
                var previousStatus = _currentStatus;
                _currentStatus = spotifyStatus;

                Logger.Info("StatusManager", $"Status updated: {previousStatus} -> {spotifyStatus}");

                CheckForSessionLoss(spotifyStatus, previousStatus);
                UpdateIcons(spotifyStatus);
                LogStatusChange(spotifyStatus);
                OnStatusChanged(new ApplicationStatusChangedEventArgs(previousStatus, spotifyStatus));
            }
            catch (Exception ex)
            {
                Logger.Error("StatusManager", ex, "Error updating Spotify status");
                HandleError("Failed to update status", ex);
            }
        }

        private void CheckForSessionLoss(SpotifyLoginStatus current, SpotifyLoginStatus previous)
        {
            if (current == SpotifyLoginStatus.NotLoggedIn && previous == SpotifyLoginStatus.LoggedIn)
            {
                var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                Logger.Warning("StatusManager", $"[{timestamp}] Spotify session lost - user authentication required for Spotify features");
            }
        }

        private void UpdateIcons(SpotifyLoginStatus status)
        {
            _systemTrayManager.SetStatusIcon(status);
            UpdateMainWindowIcon(status);
        }

        public void HandleError(string errorMessage, Exception? exception = null)
        {
            try
            {
                if (exception != null)
                {
                    Logger.Error("StatusManager", exception, errorMessage);
                }
                else
                {
                    Logger.Error("StatusManager", errorMessage);
                }

                var previousStatus = _currentStatus;
                _currentStatus = SpotifyLoginStatus.Error;

                UpdateIcons(SpotifyLoginStatus.Error);
                OnStatusChanged(new ApplicationStatusChangedEventArgs(previousStatus, SpotifyLoginStatus.Error));
            }
            catch (Exception ex)
            {
                Logger.Error("StatusManager", ex, "Critical error in error handler");
            }
        }

        private void UpdateMainWindowIcon(SpotifyLoginStatus status)
        {
            try
            {
                var mainWindow = WpfApplication.Current.MainWindow;
                if (mainWindow == null) return;

                var iconPath = GetIconPath(status);

                if (File.Exists(iconPath))
                {
                    try
                    {
                        var iconUri = new Uri($"pack://application:,,,/{iconPath}");
                        mainWindow.Icon = new BitmapImage(iconUri);
                        Logger.Debug("StatusManager", $"Main window icon updated to: {iconPath}");
                    }
                    catch (Exception ex)
                    {
                        Logger.Warning("StatusManager", $"Could not update main window icon: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error("StatusManager", ex, "Error updating main window icon");
            }
        }

        private string GetIconPath(SpotifyLoginStatus status)
        {
            return status switch
            {
                SpotifyLoginStatus.LoggedIn => "Resources/app_icon_green.ico",
                SpotifyLoginStatus.NotLoggedIn or SpotifyLoginStatus.Error => "Resources/app_icon_red.ico",
                _ => "Resources/app_icon.ico"
            };
        }

        private void LogStatusChange(SpotifyLoginStatus status)
        {
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            var statusMessage = status switch
            {
                SpotifyLoginStatus.LoggedIn => "User is logged into Spotify",
                SpotifyLoginStatus.NotLoggedIn => "User is not logged into Spotify",
                SpotifyLoginStatus.Error => "Application error state detected",
                _ => "Status unknown"
            };

            Logger.Info("StatusManager", $"[{timestamp}] STATUS: {statusMessage}");
        }

        public bool EnsureSpotifyAvailable(string operation)
        {
            if (_currentStatus == SpotifyLoginStatus.LoggedIn)
            {
                return true;
            }

            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            Logger.Error("StatusManager", $"[{timestamp}] ERROR: Spotify login required for '{operation}' but user is not authenticated");
            return false;
        }

        public string GetStatusMessage()
        {
            return _currentStatus switch
            {
                SpotifyLoginStatus.LoggedIn => "Spotify: Connected",
                SpotifyLoginStatus.NotLoggedIn => "Spotify: Not Connected",
                SpotifyLoginStatus.Error => "Application Error",
                _ => "Checking Status..."
            };
        }

        protected virtual void OnStatusChanged(ApplicationStatusChangedEventArgs args)
        {
            StatusChanged?.Invoke(this, args);
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _systemTrayManager?.Dispose();
                _disposed = true;
            }
        }
    }

    public class ApplicationStatusChangedEventArgs : EventArgs
    {
        public SpotifyLoginStatus PreviousStatus { get; }
        public SpotifyLoginStatus NewStatus { get; }

        public ApplicationStatusChangedEventArgs(SpotifyLoginStatus previousStatus, SpotifyLoginStatus newStatus)
        {
            PreviousStatus = previousStatus;
            NewStatus = newStatus;
        }
    }
}
