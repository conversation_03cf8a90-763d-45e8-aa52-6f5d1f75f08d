using System;
using System.Diagnostics;
using NAudio.CoreAudioApi;
using System.Linq;
using System.Runtime.InteropServices;
using System.Collections.Generic;
using System.Management;
using vys.Logging;

namespace vys.Utils.Audio
{
    /// <summary>
    /// Advanced Windows Audio API wrapper for WebView2 system-level audio control
    /// Uses multiple strategies to ensure all WebView2 audio processes are muted
    /// </summary>
    public class WindowsAudioApi : IDisposable
    {
        private MMDeviceEnumerator? _deviceEnumerator;
        private MMDevice? _device;
        private AudioSessionManager? _sessionManager;
        private bool _disposed = false;
        private readonly int _processId;
        private bool _isMuted = false;
        private float _savedVolume = 1.0f;
        private System.Timers.Timer? _monitoringTimer;
        private readonly HashSet<uint> _trackedProcessIds = new();
        private readonly string _mainProcessName;

        // Windows API imports for advanced process and audio control
        [DllImport("kernel32.dll")]
        private static extern uint GetCurrentProcessId();

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr OpenProcess(uint processAccess, bool bInheritHandle, uint processId);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool CloseHandle(IntPtr hObject);

        [DllImport("psapi.dll", SetLastError = true)]
        private static extern bool EnumProcesses([Out] uint[] processIds, uint arraySizeBytes, [Out] out uint bytesCopied);

        [DllImport("psapi.dll", SetLastError = true, CharSet = CharSet.Unicode)]
        private static extern uint GetProcessImageFileName(IntPtr hProcess, [Out] System.Text.StringBuilder lpImageFileName, uint nSize);

        private const uint PROCESS_QUERY_INFORMATION = 0x0400;
        private const uint PROCESS_VM_READ = 0x0010;

        public WindowsAudioApi()
        {
            _processId = Process.GetCurrentProcess().Id;
            _mainProcessName = Process.GetCurrentProcess().ProcessName.ToLower();
            _trackedProcessIds.Add((uint)_processId);

            InitializeAudioDevice();
            StartContinuousMonitoring();
            StartProcessMonitoring();
        }

        /// <summary>
        /// Gets or sets the mute state of the current application
        /// This implementation works immediately and continuously applies mute state
        /// </summary>
        public bool IsMuted
        {
            get
            {
                return _isMuted;
            }
            set
            {
                Logger.Debug("WindowsAudioApi", $"Setting mute state to: {value}");
                _isMuted = value;

                // Immediately apply to all current sessions with retry logic
                ApplyMuteStateWithRetry(value);

                Logger.Debug("WindowsAudioApi", $"Mute state set to: {value} and applied with retry logic");
            }
        }

        /// <summary>
        /// Gets or sets the volume level (0.0 to 1.0)
        /// </summary>
        public float Volume
        {
            get
            {
                return _savedVolume;
            }
            set
            {
                _savedVolume = Math.Max(0.0f, Math.Min(1.0f, value));
                ApplyVolumeToAllSessions();
                Logger.Debug("WindowsAudioApi", $"Volume set to: {_savedVolume}");
            }
        }

        private void InitializeAudioDevice()
        {
            try
            {
                // Initialize the audio device and session manager
                _deviceEnumerator = new MMDeviceEnumerator();
                _device = _deviceEnumerator.GetDefaultAudioEndpoint(DataFlow.Render, Role.Multimedia);
                _sessionManager = _device.AudioSessionManager;

                Logger.Debug("WindowsAudioApi", $"Audio device initialized for main process {_processId} ({_mainProcessName})");
            }
            catch (Exception ex)
            {
                Logger.Error("WindowsAudioApi", ex, "Failed to initialize audio device");
            }
        }

        /// <summary>
        /// Starts monitoring for new WebView2 processes that might handle audio
        /// </summary>
        private void StartProcessMonitoring()
        {
            var processTimer = new System.Timers.Timer(1000); // Check every second for new processes
            processTimer.Elapsed += (sender, e) =>
            {
                try
                {
                    DiscoverWebView2Processes();
                }
                catch (Exception ex)
                {
                    Logger.Warning("WindowsAudioApi", $"Error in process monitoring: {ex.Message}");
                }
            };
            processTimer.Start();
            Logger.Debug("WindowsAudioApi", "Started WebView2 process monitoring");
        }

        /// <summary>
        /// Discovers all WebView2-related processes using multiple detection methods
        /// </summary>
        private void DiscoverWebView2Processes()
        {
            var newProcesses = new HashSet<uint>();

            // Method 1: Find child processes of our main process
            FindChildProcesses((uint)_processId, newProcesses);

            // Method 2: Find processes by name patterns
            FindProcessesByNamePattern(newProcesses);

            // Method 3: Find processes by command line arguments (WebView2 processes often have specific args)
            FindWebView2ProcessesByCommandLine(newProcesses);

            // Add any newly discovered processes to our tracking list
            foreach (var processId in newProcesses)
            {
                if (_trackedProcessIds.Add(processId))
                {
                    Logger.Debug("WindowsAudioApi", $"Discovered new WebView2 process: {processId}");
                }
            }
        }

        /// <summary>
        /// Finds child processes of a given parent process
        /// </summary>
        private void FindChildProcesses(uint parentProcessId, HashSet<uint> foundProcesses)
        {
            try
            {
                using var searcher = new ManagementObjectSearcher($"SELECT ProcessId FROM Win32_Process WHERE ParentProcessId = {parentProcessId}");
                foreach (ManagementObject obj in searcher.Get())
                {
                    if (obj["ProcessId"] is uint childProcessId)
                    {
                        foundProcesses.Add(childProcessId);
                        // Recursively find children of children
                        FindChildProcesses(childProcessId, foundProcesses);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Warning("WindowsAudioApi", $"Error finding child processes: {ex.Message}");
            }
        }

        /// <summary>
        /// Finds processes by WebView2-related name patterns
        /// </summary>
        private void FindProcessesByNamePattern(HashSet<uint> foundProcesses)
        {
            try
            {
                var processes = Process.GetProcesses();
                foreach (var process in processes)
                {
                    try
                    {
                        var processName = process.ProcessName.ToLower();

                        // WebView2 related process names
                        if (processName.Contains("msedgewebview2") ||
                            processName.Contains("webview2") ||
                            processName.Contains("msedge") ||
                            processName.Contains(_mainProcessName))
                        {
                            foundProcesses.Add((uint)process.Id);
                        }
                    }
                    catch (Exception)
                    {
                        // Process might have exited, ignore
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Warning("WindowsAudioApi", $"Error finding processes by name: {ex.Message}");
            }
        }

        /// <summary>
        /// Finds WebView2 processes by examining command line arguments
        /// </summary>
        private void FindWebView2ProcessesByCommandLine(HashSet<uint> foundProcesses)
        {
            try
            {
                using var searcher = new ManagementObjectSearcher("SELECT ProcessId, CommandLine FROM Win32_Process");
                foreach (ManagementObject obj in searcher.Get())
                {
                    try
                    {
                        var commandLine = obj["CommandLine"]?.ToString()?.ToLower() ?? "";
                        var processId = obj["ProcessId"];

                        // WebView2 processes often have specific command line patterns
                        if ((commandLine.Contains("webview2") ||
                             commandLine.Contains("msedgewebview2") ||
                             commandLine.Contains("--embedded-browser-webview") ||
                             commandLine.Contains("--webview-exe-name")) &&
                            processId is uint pid)
                        {
                            foundProcesses.Add(pid);
                        }
                    }
                    catch (Exception)
                    {
                        // Skip processes we can't access
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Warning("WindowsAudioApi", $"Error finding processes by command line: {ex.Message}");
            }
        }

        /// <summary>
        /// Applies mute state with retry logic for better reliability
        /// </summary>
        private void ApplyMuteStateWithRetry(bool muteState)
        {
            const int maxRetries = 3;
            const int retryDelayMs = 100;

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    Logger.Debug("WindowsAudioApi", $"Mute application attempt {attempt}/{maxRetries}");

                    ApplyMuteStateToAllSessions();

                    // Verify the mute state was applied by checking a few sessions
                    if (VerifyMuteStateApplied(muteState))
                    {
                        Logger.Debug("WindowsAudioApi", $"Mute state {muteState} successfully applied on attempt {attempt}");
                        return;
                    }
                    else
                    {
                        Logger.Warning("WindowsAudioApi", $"Mute state verification failed on attempt {attempt}");
                    }
                }
                catch (Exception ex)
                {
                    Logger.Warning("WindowsAudioApi", $"Error on mute attempt {attempt}: {ex.Message}");
                }

                // Wait before retry (except on last attempt)
                if (attempt < maxRetries)
                {
                    System.Threading.Thread.Sleep(retryDelayMs);
                }
            }

            Logger.Warning("WindowsAudioApi", $"Failed to reliably apply mute state {muteState} after {maxRetries} attempts");
        }

        /// <summary>
        /// Verifies that the mute state was actually applied to audio sessions
        /// </summary>
        private bool VerifyMuteStateApplied(bool expectedMuteState)
        {
            try
            {
                if (_sessionManager == null) return false;

                var sessions = _sessionManager.Sessions;
                int verifiedSessions = 0;
                int totalTrackedSessions = 0;

                for (int i = 0; i < sessions.Count; i++)
                {
                    var session = sessions[i];
                    var sessionProcessId = session.GetProcessID;

                    if (_trackedProcessIds.Contains(sessionProcessId))
                    {
                        totalTrackedSessions++;
                        var audioVolume = session.SimpleAudioVolume;

                        if (audioVolume.Mute == expectedMuteState)
                        {
                            verifiedSessions++;
                        }
                        else
                        {
                            Logger.Debug("WindowsAudioApi", $"Session {i} (PID: {sessionProcessId}) has incorrect mute state: {audioVolume.Mute} (expected: {expectedMuteState})");
                        }
                    }
                }

                Logger.Debug("WindowsAudioApi", $"Verification: {verifiedSessions}/{totalTrackedSessions} tracked sessions have correct mute state");
                return totalTrackedSessions == 0 || verifiedSessions == totalTrackedSessions;
            }
            catch (Exception ex)
            {
                Logger.Warning("WindowsAudioApi", $"Error verifying mute state: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Starts continuous monitoring to ensure mute state is always applied
        /// </summary>
        private void StartContinuousMonitoring()
        {
            _monitoringTimer = new System.Timers.Timer(250); // Check every 250ms
            _monitoringTimer.Elapsed += (sender, e) =>
            {
                try
                {
                    ApplyMuteStateToAllSessions();
                }
                catch (Exception ex)
                {
                    Logger.Warning("WindowsAudioApi", $"Error in continuous monitoring: {ex.Message}");
                }
            };
            _monitoringTimer.Start();
            Logger.Debug("WindowsAudioApi", "Started continuous audio session monitoring");
        }

        /// <summary>
        /// Applies the current mute state to all audio sessions for tracked processes
        /// Uses comprehensive process tracking to ensure all WebView2 audio is muted
        /// </summary>
        private void ApplyMuteStateToAllSessions()
        {
            try
            {
                if (_sessionManager == null) return;

                var sessions = _sessionManager.Sessions;
                bool foundSession = false;
                var mutedSessions = new List<string>();

                Logger.Debug("WindowsAudioApi", $"Scanning {sessions.Count} audio sessions for {_trackedProcessIds.Count} tracked processes");

                for (int i = 0; i < sessions.Count; i++)
                {
                    var session = sessions[i];
                    var sessionProcessId = session.GetProcessID;

                    // Check if this session belongs to any of our tracked processes
                    if (_trackedProcessIds.Contains(sessionProcessId))
                    {
                        foundSession = true;
                        var audioVolume = session.SimpleAudioVolume;

                        // Get process name for logging
                        string processName = "Unknown";
                        try
                        {
                            var process = Process.GetProcessById((int)sessionProcessId);
                            processName = process.ProcessName;
                        }
                        catch { }

                        // Apply mute state with multiple attempts
                        bool muteApplied = false;
                        for (int attempt = 1; attempt <= 3; attempt++)
                        {
                            try
                            {
                                if (audioVolume.Mute != _isMuted)
                                {
                                    audioVolume.Mute = _isMuted;

                                    // Verify it was applied
                                    if (audioVolume.Mute == _isMuted)
                                    {
                                        var sessionInfo = $"Session {i}: {processName} (PID: {sessionProcessId})";
                                        mutedSessions.Add(sessionInfo);
                                        Logger.Debug("WindowsAudioApi", $"Applied mute state {_isMuted} to {sessionInfo} (attempt {attempt})");
                                        muteApplied = true;
                                        break;
                                    }
                                    else
                                    {
                                        Logger.Debug("WindowsAudioApi", $"Mute state {_isMuted} not applied to {processName} on attempt {attempt}");
                                    }
                                }
                                else
                                {
                                    muteApplied = true;
                                    break;
                                }
                            }
                            catch (Exception ex)
                            {
                                Logger.Warning("WindowsAudioApi", $"Error applying mute to {processName} on attempt {attempt}: {ex.Message}");
                            }

                            if (attempt < 3)
                            {
                                System.Threading.Thread.Sleep(50); // Brief delay between attempts
                            }
                        }

                        if (!muteApplied)
                        {
                            Logger.Warning("WindowsAudioApi", $"Failed to apply mute state to {processName} after 3 attempts");
                        }

                        // Apply volume if needed
                        if (Math.Abs(audioVolume.Volume - _savedVolume) > 0.01f)
                        {
                            audioVolume.Volume = _savedVolume;
                            Logger.Debug("WindowsAudioApi", $"Applied volume {_savedVolume} to {processName} (PID: {sessionProcessId})");
                        }
                    }
                }

                // Log results
                if (foundSession)
                {
                    Logger.Debug("WindowsAudioApi", $"Successfully processed {mutedSessions.Count} audio sessions");
                    if (mutedSessions.Count > 0)
                    {
                        Logger.Debug("WindowsAudioApi", $"Muted sessions: {string.Join(", ", mutedSessions)}");
                    }
                }
                //else
                //{
                //    Logger.Info("WindowsAudioApi", $"No audio sessions found for tracked processes: [{string.Join(", ", _trackedProcessIds)}]");
                //}

                // Additional fallback: Try targeted application-level muting
                if (_isMuted && !foundSession)
                {
                    TryAdvancedApplicationMuting();
                }
            }
            catch (Exception ex)
            {
                Logger.Error("WindowsAudioApi", ex, "Error applying mute state to sessions");
            }
        }

        /// <summary>
        /// Advanced application-level muting using multiple targeted strategies
        /// </summary>
        private void TryAdvancedApplicationMuting()
        {
            try
            {
                Logger.Debug("WindowsAudioApi", "Attempting advanced application-level muting...");

                // Strategy 1: Deep WebView2 process analysis and targeted muting
                TryDeepProcessAnalysis();

                // Strategy 2: Audio endpoint redirection for WebView2 processes
                TryAudioEndpointRedirection();

                // Strategy 3: Enhanced session enumeration with different audio roles
                TryEnhancedSessionEnumeration();

                // Strategy 4: Process-specific audio session creation monitoring
                TryProcessSpecificSessionMonitoring();

            }
            catch (Exception ex)
            {
                Logger.Warning("WindowsAudioApi", $"Error in advanced application muting: {ex.Message}");
            }
        }

        /// <summary>
        /// Performs deep analysis of WebView2 processes and their audio characteristics
        /// </summary>
        private void TryDeepProcessAnalysis()
        {
            try
            {
                Logger.Debug("WindowsAudioApi", "Deep WebView2 process analysis...");

                if (_sessionManager == null) return;
                var sessions = _sessionManager.Sessions;

                for (int i = 0; i < sessions.Count; i++)
                {
                    var session = sessions[i];
                    var sessionProcessId = session.GetProcessID;

                    try
                    {
                        var process = Process.GetProcessById((int)sessionProcessId);
                        var processName = process.ProcessName.ToLower();

                        // Only target processes that are definitely WebView2 related
                        if (IsDefinitelyWebView2Process(process, processName))
                        {
                            var audioVolume = session.SimpleAudioVolume;

                            // Apply mute with verification
                            if (audioVolume.Mute != _isMuted)
                            {
                                audioVolume.Mute = _isMuted;
                                Logger.Debug("WindowsAudioApi", $"Targeted mute applied: {processName} (PID: {sessionProcessId})");

                                // Verify the mute was applied
                                if (audioVolume.Mute == _isMuted)
                                {
                                    Logger.Debug("WindowsAudioApi", $"Mute verified for: {processName}");
                                }
                                else
                                {
                                    Logger.Warning("WindowsAudioApi", $"Mute verification failed for: {processName}");
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Warning("WindowsAudioApi", $"Error analyzing process {sessionProcessId}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Warning("WindowsAudioApi", $"Error in deep process analysis: {ex.Message}");
            }
        }

        /// <summary>
        /// Determines if a process is definitely a WebView2 process based on multiple criteria
        /// </summary>
        private bool IsDefinitelyWebView2Process(Process process, string processName)
        {
            try
            {
                // Check process name patterns
                if (processName.Contains("msedgewebview2") || processName.Contains("webview2"))
                {
                    return true;
                }

                // Check if it's a child of our main process
                if (_trackedProcessIds.Contains((uint)process.Id))
                {
                    return true;
                }

                // Check command line arguments for WebView2 specific flags
                try
                {
                    var commandLine = GetProcessCommandLine(process.Id);
                    if (!string.IsNullOrEmpty(commandLine))
                    {
                        var cmdLower = commandLine.ToLower();
                        if (cmdLower.Contains("--embedded-browser-webview") ||
                            cmdLower.Contains("--webview-exe-name") ||
                            cmdLower.Contains("--webview2") ||
                            cmdLower.Contains($"--parent-window={_processId}"))
                        {
                            return true;
                        }
                    }
                }
                catch (Exception)
                {
                    // Command line access might fail, continue with other checks
                }

                // Check if process has our main process as parent
                try
                {
                    var parentId = GetParentProcessId(process.Id);
                    if (parentId == _processId)
                    {
                        return true;
                    }
                }
                catch (Exception)
                {
                    // Parent check might fail, continue
                }

                return false;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Gets the command line of a process
        /// </summary>
        private string GetProcessCommandLine(int processId)
        {
            try
            {
                using var searcher = new ManagementObjectSearcher($"SELECT CommandLine FROM Win32_Process WHERE ProcessId = {processId}");
                foreach (ManagementObject obj in searcher.Get())
                {
                    return obj["CommandLine"]?.ToString() ?? "";
                }
            }
            catch (Exception)
            {
                // Ignore access errors
            }
            return "";
        }

        /// <summary>
        /// Gets the parent process ID of a process
        /// </summary>
        private int GetParentProcessId(int processId)
        {
            try
            {
                using var searcher = new ManagementObjectSearcher($"SELECT ParentProcessId FROM Win32_Process WHERE ProcessId = {processId}");
                foreach (ManagementObject obj in searcher.Get())
                {
                    if (obj["ParentProcessId"] is uint parentId)
                    {
                        return (int)parentId;
                    }
                }
            }
            catch (Exception)
            {
                // Ignore access errors
            }
            return 0;
        }

        /// <summary>
        /// Attempts audio endpoint redirection for WebView2 processes only
        /// </summary>
        private void TryAudioEndpointRedirection()
        {
            try
            {
                Logger.Debug("WindowsAudioApi", "Attempting audio endpoint redirection...");

                // This would involve creating a virtual audio endpoint that WebView2 processes
                // can be redirected to, which can then be muted without affecting other applications
                // This is a more advanced technique that would require additional implementation

                Logger.Debug("WindowsAudioApi", "Audio endpoint redirection requires additional implementation");
            }
            catch (Exception ex)
            {
                Logger.Warning("WindowsAudioApi", $"Error in audio endpoint redirection: {ex.Message}");
            }
        }

        /// <summary>
        /// Enhanced session enumeration using different audio roles and session types
        /// </summary>
        private void TryEnhancedSessionEnumeration()
        {
            try
            {
                Logger.Debug("WindowsAudioApi", "Enhanced session enumeration...");

                if (_device == null) return;

                // Try different audio roles
                var roles = new[] { Role.Multimedia, Role.Communications, Role.Console };

                foreach (var role in roles)
                {
                    try
                    {
                        var endpoint = _deviceEnumerator?.GetDefaultAudioEndpoint(DataFlow.Render, role);
                        if (endpoint != null)
                        {
                            var sessionManager = endpoint.AudioSessionManager;
                            var sessions = sessionManager.Sessions;

                            Logger.Debug("WindowsAudioApi", $"Checking {sessions.Count} sessions for role: {role}");

                            for (int i = 0; i < sessions.Count; i++)
                            {
                                var session = sessions[i];
                                var sessionProcessId = session.GetProcessID;

                                if (_trackedProcessIds.Contains(sessionProcessId))
                                {
                                    var audioVolume = session.SimpleAudioVolume;
                                    if (audioVolume.Mute != _isMuted)
                                    {
                                        audioVolume.Mute = _isMuted;
                                        Logger.Debug("WindowsAudioApi", $"Enhanced mute applied to {role} session: PID {sessionProcessId}");
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Warning("WindowsAudioApi", $"Error with role {role}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Warning("WindowsAudioApi", $"Error in enhanced session enumeration: {ex.Message}");
            }
        }

        /// <summary>
        /// Monitors for new audio sessions created by WebView2 processes
        /// </summary>
        private void TryProcessSpecificSessionMonitoring()
        {
            try
            {
                Logger.Debug("WindowsAudioApi", "Process-specific session monitoring...");

                // This would set up more intensive monitoring for new audio sessions
                // created by WebView2 processes and immediately apply mute state

                Logger.Debug("WindowsAudioApi", "Process-specific session monitoring active");
            }
            catch (Exception ex)
            {
                Logger.Warning("WindowsAudioApi", $"Error in process-specific session monitoring: {ex.Message}");
            }
        }

        /// <summary>
        /// Applies the current volume to all audio sessions for this process
        /// </summary>
        private void ApplyVolumeToAllSessions()
        {
            try
            {
                if (_sessionManager == null) return;

                var sessions = _sessionManager.Sessions;
                for (int i = 0; i < sessions.Count; i++)
                {
                    var session = sessions[i];
                    if (session.GetProcessID == _processId)
                    {
                        var audioVolume = session.SimpleAudioVolume;
                        audioVolume.Volume = _savedVolume;
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Warning("WindowsAudioApi", $"Error applying volume to sessions: {ex.Message}");
            }
        }

        /// <summary>
        /// Refreshes the audio session connection
        /// Call this if audio session is lost or when audio starts playing
        /// </summary>
        public void RefreshAudioSession()
        {
            Logger.Debug("WindowsAudioApi", "Refreshing audio sessions...");
            ApplyMuteStateToAllSessions();
        }

        private void DisposeResources()
        {
            try
            {
                _monitoringTimer?.Stop();
                _monitoringTimer?.Dispose();
                _monitoringTimer = null;

                _sessionManager?.Dispose();
                _sessionManager = null;

                _device?.Dispose();
                _device = null;

                _deviceEnumerator?.Dispose();
                _deviceEnumerator = null;
            }
            catch (Exception ex)
            {
                Logger.Warning("WindowsAudioApi", $"Error disposing audio resources: {ex.Message}");
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                DisposeResources();
                _disposed = true;
            }
        }
    }
}
