# Spotify WebView2 Automation Guide

## Overview

I've created a comprehensive human-like automation system for your VYS Browser application that can interact with Spotify through the WebView2 component. This system simulates real human interactions like clicking, typing, and scrolling with realistic delays and behavior patterns.

## What's Been Added

### 1. Core Automation Classes

- **`WebView2Automation.cs`** - Base automation class with human-like interaction methods
- **`SpotifyAutomation.cs`** - Spotify-specific automation methods
- **`SpotifyAutomationDemo.cs`** - Example usage and demo scenarios

### 2. Integration with MainWindow

Your `MainWindow.xaml.cs` now includes these new public methods:

- `SpotifyGoToDashboardAsync()` - Navigate to Spotify dashboard
- `SpotifyPlaylistExistsAsync(playlistName)` - Check if playlist exists
- `SpotifyCreatePlaylistAsync(playlistName)` - Create new playlists
- `SpotifyDeletePlaylistAsync(playlistName)` - Delete playlists (via dashboard)
- `SpotifyPlayPlaylistAsync(playlistName)` - Play a playlist by navigating to it and clicking play button
- `SpotifyListPlaylistSongsAsync(playlistName)` - List all songs in a playlist with detailed information
- `SpotifySearchAsync(searchTerm)` - Search for songs/artists (types character-by-character and presses Enter)
- `SpotifySearchWithValidationAsync(searchTerm)` - Search with validation that results appear (recommended for add song operations)
- `SpotifyGetCurrentSongAsync()` - Get currently playing song info
- `SpotifyAddSongToPlaylistAsync(playlistId, trackUri)` - Add a song to a playlist by track URI
- `SpotifyGetFirstSearchResultTrackUriAsync()` - Get track URI from first search result
- `SpotifyGetCurrentPlaylistIdAsync()` - Get current playlist ID from URL
- `SpotifySearchAndAddSongToPlaylistAsync(searchTerm, playlistId)` - Search and add first result to playlist
- `ExecuteCustomScriptAsync(script)` - Execute custom JavaScript

### 3. New UI Controls

The application header now includes automation buttons:

- **Playlist Name TextBox** - Enter playlist names for create/delete/play operations
- **➕ Create Button** - Creates a new playlist with the specified name
- **🗑️ Delete Button** - Deletes a playlist (checks existence first, shows confirmation)
- **▶️ Play Button** - Plays a playlist by navigating to it and clicking the play button
- **Search TextBox** - Enter search terms for songs/artists
- **🔍 Search Button** - Executes search on Spotify
- **🏠 Home Button** - Navigates to Spotify dashboard
- **🧪 Test Button** - Tests dashboard navigation success

### Quick Play and List Songs Header Section

- **🎵 Quick Play TextBox** - Dedicated text box in the header for instant playlist playback
  - Green styling for easy identification
- **▶️ Play Button** - Immediately plays the specified playlist without additional steps
- **📋 List Songs TextBox** - Dedicated text box for listing all songs in a playlist
  - Blue styling to distinguish from Quick Play
- **📋 List Songs Button** - Lists all songs in the specified playlist with detailed information

## How to Use

### Basic Usage Example

```csharp
// Get reference to your main window
var mainWindow = Application.Current.MainWindow as MainWindow;

// Navigate to dashboard first
await mainWindow.SpotifyGoToDashboardAsync();

// Check if playlist exists
var exists = await mainWindow.SpotifyPlaylistExistsAsync("My Awesome Playlist");
if (!exists)
{
    // Create a playlist
    var createSuccess = await mainWindow.SpotifyCreatePlaylistAsync("My Awesome Playlist");

    if (createSuccess)
    {
        // Search for a song
        await mainWindow.SpotifySearchAsync("Bohemian Rhapsody Queen");

        // Wait for search results
        await Task.Delay(2000);

        // Search results are now displayed

        // Get current song info
        var currentSong = await mainWindow.SpotifyGetCurrentSongAsync();
        Console.WriteLine($"Now playing: {currentSong}");

        // Play the playlist
        await mainWindow.SpotifyPlayPlaylistAsync("My Awesome Playlist");

        // Delete the playlist later (automatically navigates to dashboard)
        await mainWindow.SpotifyDeletePlaylistAsync("My Awesome Playlist");
    }
}

// Example: Adding songs to an existing playlist
var playlistId = "37i9dQZF1DXcBWIGoYBM5M"; // Example playlist ID
var success = await mainWindow.SpotifySearchAndAddSongToPlaylistAsync("Bohemian Rhapsody Queen", playlistId);

// Example: List all songs in a playlist
var songs = await mainWindow.SpotifyListPlaylistSongsAsync("My Favorite Songs");
foreach (var song in songs)
{
    var title = song.ContainsKey("title") ? song["title"] : "Unknown";
    var artist = song.ContainsKey("artist") ? song["artist"] : "Unknown";
    var album = song.ContainsKey("album") ? song["album"] : "Unknown";
    var duration = song.ContainsKey("duration") ? song["duration"] : "Unknown";
    Console.WriteLine($"{title} by {artist} (Album: {album}) [{duration}]");
}

// Alternative: Manual approach with more control
await mainWindow.SpotifySearchAsync("Hotel California Eagles");
await Task.Delay(2000); // Wait for search results
var trackUri = await mainWindow.SpotifyGetFirstSearchResultTrackUriAsync();
if (!string.IsNullOrEmpty(trackUri))
{
    var currentPlaylistId = await mainWindow.SpotifyGetCurrentPlaylistIdAsync();
    if (!string.IsNullOrEmpty(currentPlaylistId))
    {
        await mainWindow.SpotifyAddSongToPlaylistAsync(currentPlaylistId, trackUri);
    }
}
```

### Using the UI Buttons

The application now has a user-friendly interface with buttons for common operations:

1. **Create Playlist**: Enter a name in the text box and click "➕ Create"
2. **Delete Playlist**: Enter the playlist name and click "🗑️ Delete" (shows confirmation)
3. **Play Playlist**: Enter the playlist name and click "▶️ Play" to start playing the playlist
4. **Quick Play**: Use the dedicated "🎵 Quick Play" section in the header for instant playlist playback
5. **List Songs**: Use the dedicated "📋 List Songs" section in the header to view all songs in a playlist
5. **Search**: Enter search terms and click "🔍 Search"

All operations show status updates in the bottom status bar.

### Using the Demo Class

```csharp
var demo = new SpotifyAutomationDemo(mainWindow);

// Demonstrate playlist management
await demo.DemoPlaylistManagementAsync("Test Playlist");

// Search workflow (no login required)
await demo.DemoSearchAsync("your favorite song");

// Test dashboard navigation
await demo.DemoDashboardNavigationTestAsync();

// Play a playlist
await demo.DemoPlayPlaylistAsync("My Favorite Songs");

// List all songs in a playlist
await demo.DemoListPlaylistSongsAsync("My Favorite Songs");

// Play multiple songs
await demo.DemoMultipleSongsAsync(new[] { "Song 1", "Song 2", "Song 3" });
```

### Custom JavaScript Execution

```csharp
// Get page information
var title = await mainWindow.ExecuteCustomScriptAsync("document.title");

// Check login status
var loginCheck = await mainWindow.ExecuteCustomScriptAsync(@"
    (function() {
        const userWidget = document.querySelector('[data-testid=""user-widget-link""]');
        return userWidget !== null;
    })();
");

// Custom interactions
var customScript = await mainWindow.ExecuteCustomScriptAsync(@"
    (function() {
        // Your custom JavaScript here
        const element = document.querySelector('.some-selector');
        if (element) {
            element.click();
            return 'Success';
        }
        return 'Element not found';
    })();
");
```

## Key Features

### Human-Like Behavior

- **Realistic delays** - Random delays between 200-800ms for actions
- **Mouse simulation** - Simulates mouseover, mousedown, mouseup, and click events
- **Typing patterns** - Types character by character with 50-200ms delays
- **Scroll behavior** - Smoothly scrolls elements into view
- **Random positioning** - Adds slight randomness to click positions

### Anti-Detection Features

- **Event simulation** - Fires proper DOM events (keydown, keypress, input, keyup)
- **Focus management** - Properly focuses elements before interaction
- **Realistic timing** - Variable delays that mimic human behavior
- **Smooth scrolling** - Uses browser's smooth scroll behavior

### Error Handling

- **Comprehensive logging** - All actions are logged with debug information
- **Graceful failures** - Methods return boolean success indicators
- **Element waiting** - Waits for elements to appear before interacting
- **Multiple selectors** - Tries different CSS selectors for robustness

## Advanced Usage

### Custom Element Selectors

You can target specific elements using CSS selectors:

```csharp
// Click a specific song from search results
await mainWindow.ExecuteCustomScriptAsync(@"
    document.querySelector('[data-testid=""tracklist-row""]:nth-child(3)').click();
");

// Click a specific playlist
await mainWindow.ExecuteCustomScriptAsync(@"
    document.querySelector('[data-testid=""playlist-name""]').click();
");
```

### Waiting for Elements

```csharp
// Wait for specific elements to appear
var automation = new WebView2Automation(mainWindow.Browser.CoreWebView2);
var elementFound = await automation.WaitForElementAsync(".some-selector", 10000);
```

### Getting Page Information

```csharp
// Get text from elements
var automation = new WebView2Automation(mainWindow.Browser.CoreWebView2);
var songTitle = await automation.GetElementTextAsync("[data-testid='now-playing-widget'] a");
```

## Integration with Your Existing Code

The automation system integrates seamlessly with your existing:

- **Spotify login detection** - Works alongside your `SpotifyLoginDetector`
- **Audio management** - Compatible with your `AudioManager`
- **Logging system** - Uses your existing `Logger` class
- **WebView2 setup** - Uses your existing `Browser.CoreWebView2`

## Next Steps

1. **Test the basic functionality** - Try the login and search methods
2. **Customize for your needs** - Modify selectors or add new methods
3. **Add keyboard shortcuts** - Integrate automation with your existing shortcuts
4. **Create automation workflows** - Build complex sequences of actions
5. **Add error recovery** - Implement retry logic for failed actions

## Troubleshooting

- **Elements not found** - Check CSS selectors in browser dev tools (F12)
- **Timing issues** - Increase delays between actions
- **Login failures** - Verify credentials and check for CAPTCHA
- **Script errors** - Check browser console for JavaScript errors

The system is designed to be robust and human-like, making it very difficult for websites to detect that it's automated rather than a real user.

## Testing

### Unit Test Structure
The project now includes comprehensive unit tests for the automation functionality:

```
vys.Tests/
├── Automation/
│   └── DashboardNavigationTests.cs    # Dashboard navigation tests
├── Mocks/
│   └── MockWebView2.cs                # Mock WebView2 for testing
└── README.md                          # Test documentation
```

### Running Tests
```bash
# Run all tests
dotnet test vys.Tests

# Run with detailed output
dotnet test vys.Tests --verbosity normal

# Use the PowerShell script
.\run-tests.ps1
```

### Test Coverage
- **8 comprehensive tests** covering success/failure scenarios
- **Dashboard detection** - Tests for "To get you started" element
- **Navigation workflows** - Complete navigate + verify tests
- **Error handling** - JavaScript error simulation
- **Edge cases** - Text casing variations, missing elements

### Test Results Validation
Tests pass when the dashboard navigation successfully finds:
```html
<a draggable="false" data-testid="see-all-link" tabindex="-1" href="/section/0JQ5DAob0JCuWaGLU6ntGf">
  To get you started
</a>
```

## Recent Updates

### Dashboard Navigation Enhancement
- **Updated `GoToDashboardAsync()`** to use the home button instead of logo
- Now targets: `<button data-testid="home-button" aria-label="Home">`
- Includes multiple fallback selectors and JavaScript-based detection
- More reliable navigation to the Spotify dashboard
- **Added comprehensive unit tests** with 8 test scenarios
