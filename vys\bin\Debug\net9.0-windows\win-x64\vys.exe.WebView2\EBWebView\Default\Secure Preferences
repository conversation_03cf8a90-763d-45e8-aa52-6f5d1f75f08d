{"extensions": {"settings": {"dgiklkfkllikcanfonkcabmbdfmgleag": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_capabilities": {"include_globs": ["https://*excel.officeapps.live.com/*", "https://*onenote.officeapps.live.com/*", "https://*powerpoint.officeapps.live.com/*", "https://*word-edit.officeapps.live.com/*", "https://*excel.officeapps.live.com.mcas.ms/*", "https://*onenote.officeapps.live.com.mcas.ms/*", "https://*word-edit.officeapps.live.com.mcas.ms/*", "https://*excel.partner.officewebapps.cn/*", "https://*onenote.partner.officewebapps.cn/*", "https://*powerpoint.partner.officewebapps.cn/*", "https://*word-edit.partner.officewebapps.cn/*", "https://*excel.gov.online.office365.us/*", "https://*onenote.gov.online.office365.us/*", "https://*powerpoint.gov.online.office365.us/*", "https://*word-edit.gov.online.office365.us/*", "https://*excel.dod.online.office365.us/*", "https://*onenote.dod.online.office365.us/*", "https://*powerpoint.dod.online.office365.us/*", "https://*word-edit.dod.online.office365.us/*", "https://*visio.partner.officewebapps.cn/*", "https://*visio.gov.online.office365.us/*", "https://*visio.dod.online.office365.us/*"], "matches": ["https://*.officeapps.live.com/*", "https://*.officeapps.live.com.mcas.ms/*", "https://*.partner.officewebapps.cn/*", "https://*.gov.online.office365.us/*", "https://*.dod.online.office365.us/*", "https://*.app.whiteboard.microsoft.com/*", "https://*.whiteboard.office.com/*", "https://*.app.int.whiteboard.microsoft.com/*", "https://*.whiteboard.office365.us/*", "https://*.dev.whiteboard.microsoft.com/*"], "permissions": ["clipboardRead", "clipboardWrite"]}, "default_locale": "en", "description": "This extension grants Microsoft web sites permission to read and write from the clipboard.", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCz4t/X7GeuP6GBpjmxndrjtzF//4CWeHlC68rkoV7hP3h5Ka6eX7ZMNlYJkSjmB5iRmPHO5kR1y7rGY8JXnRPDQh/CQNLVA7OsKeV6w+UO+vx8KGI+TrTAhzH8YGcMIsxsUjxtC4cBmprja+xDr0zVp2EMgqHu+GBKgwSRHTkDuwIDAQAB", "manifest_version": 2, "minimum_chrome_version": "77", "name": "Microsoft Clipboard Extension", "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.95\\resources\\edge_clipboard", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate", "fileSystem.readFullPath", "errorReporting", "edgeLearningToolsPrivate", "fileSystem.getCurrentEntry", "edgePdfPrivate", "edgeCertVerifierPrivate"], "explicit_host": ["edge://resources/*", "edge://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:; trusted-types edge-internal fast-html pdf-url edge-pdf-static-policy;", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "edge_pdf/index.html", "name": "Microsoft Edge PDF Viewer", "offline_enabled": true, "permissions": ["errorReporting", "chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "edgeCertVerifierPrivate", "edgeLearningToolsPrivate", "edgePdfPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getCurrentEntry"]}], "version": "1"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.95\\resources\\edge_pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "protection": {"macs": {"browser": {"show_home_button": "1181950605766611D8F1AB694D71A25B61FB89504E2D0E31BB7DB0A02ABA078A"}, "default_search_provider_data": {"template_url_data": "14C38A177FF5B07532C803C32EB191EC3EDD49043F2478556C4D7BF3956ACFE8"}, "edge": {"services": {"account_id": "0AA60061471938E634047902032593A10B26AED2564A72DECEB1344E3FAA4E8A", "last_username": "ADDFF3CB879068C594E99724C0DB36661262B6526B31C3F4EB3EB87D6B261342"}}, "enterprise_signin": {"policy_recovery_token": "058C4AB0969F6B812F601749B81BFEDA4681CB4673BE89951FBD4B529C787B94"}, "extensions": {"settings": {"dgiklkfkllikcanfonkcabmbdfmgleag": "6A3BABF4D79B4A6DA0FFD36CD9C2E556F942646C60D0C1D13BD4ECB70D713324", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "D7E2D0DDE046D71AE7D64A128FC464DBB35A440400BFA63466A87FCA5B60F3B0"}, "ui": {"developer_mode": "28FF9943A984357B676621AC805E192DD2914EEFA484BF4B34C7BF8E3DAA5A4B"}}, "google": {"services": {"last_signed_in_username": "54596D06B696D02996C18A2A5FE917F52F05BD9B815BE6199EB025D8B1C72ACA"}}, "homepage": "A4A837227C77B8761047770680F59AF99C14D86FD6BCC55FFCAD3153466F4B8C", "homepage_is_newtabpage": "BE99252CD217E58A83D25BB7628EC47576FCE27E11515A8B1697E8590FAB5C87", "media": {"cdm": {"origin_data": "5A7B117B27ED08DDFDDBBF8008F7EDBDDE7F8C5A4DD0F3A3BBF459C698CC2CD8"}, "storage_id_salt": "47D356F4E02C2056010E530AEEDF1119168DB92E3A797F325A84C0CC003175D9"}, "pinned_tabs": "1ECFB0540F2BFD183B6ACF8EF1839D355EB005E13DBD2AA48266BB1581B0C54E", "prefs": {"preference_reset_time": "B4E2BA3D96379326DB46182CB9CF35FDD3BA9F503CF2C73DA0419C596FABD2F7"}, "safebrowsing": {"incidents_sent": "80C6CCF3A3FBACE2D1286CA67EEF80F0AF0188C4D4DE360727A2EE2DE512329B"}, "search_provider_overrides": "312674CDFFCDD67EA39BDD462EB3BF7B50F63812AB345A0C0804115F3D913EBC", "session": {"restore_on_startup": "6DA48F61DD97437BC3F07B5E47EF61B99B48F5446501644747E8140165A8C11A", "startup_urls": "570A4BE425DDE69DC6F0715CBC84A61F866F61F0B67B512CB6A36748C30C4D04"}}, "super_mac": "F9FD73EB4ADC894832C4FD568B15E9E5D2B889B8ECF7BE29F8738D06E28FA1A2"}}