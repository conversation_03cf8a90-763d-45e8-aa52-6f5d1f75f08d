using System;
using System.Collections.Generic;
using System.Linq;
using vys.Logging;

namespace vys.Utils
{
    /// <summary>
    /// Utility class for generating creative and random playlist names
    /// </summary>
    public static class PlaylistNameGenerator
    {
        private static readonly Random _random = new Random();

        #region Word Collections

        private static readonly string[] _moodAdjectives = {
            "Chill", "Energetic", "Mellow", "Vibrant", "Dreamy", "Electric", "Smooth", "Intense",
            "Peaceful", "Dynamic", "Soothing", "Powerful", "Relaxed", "Uplifting", "Ambient",
            "Groovy", "Funky", "Ethereal", "Pulsing", "Ser<PERSON>", "Wild", "Co<PERSON>", "Neon",
            "Golden", "Midnight", "Dawn", "Sunset", "Urban", "Vintage", "Modern", "Classic"
        };

        private static readonly string[] _moodNouns = {
            "Vibes", "Beats", "Waves", "Dreams", "Journey", "Sessions", "Escape", "Flow",
            "Rhythm", "Pulse", "Energy", "Atmosphere", "Mood", "Spirit", "Soul", "Heart",
            "Mind", "Space", "Zone", "World", "Universe", "Galaxy", "Ocean", "Sky",
            "Mountain", "Valley", "Forest", "Desert", "City", "Highway", "Boulevard"
        };

        private static readonly string[] _timeBasedPrefixes = {
            "Morning", "Afternoon", "Evening", "Night", "Late Night", "Early Morning",
            "Sunrise", "Sunset", "Midnight", "Dawn", "Dusk", "Twilight", "Weekend",
            "Monday", "Friday", "Summer", "Winter", "Spring", "Autumn", "Holiday"
        };

        private static readonly string[] _timeBasedSuffixes = {
            "Coffee", "Drive", "Walk", "Sessions", "Playlist", "Mix", "Collection",
            "Soundtrack", "Companion", "Journey", "Adventure", "Escape", "Moments",
            "Memories", "Stories", "Tales", "Chronicles", "Diary", "Log", "Notes"
        };

        private static readonly string[] _genreDescriptors = {
            "Rock", "Pop", "Jazz", "Blues", "Electronic", "Hip-Hop", "R&B", "Country",
            "Folk", "Classical", "Indie", "Alternative", "Punk", "Metal", "Reggae",
            "Soul", "Funk", "Disco", "House", "Techno", "Ambient", "Acoustic", "Vocal"
        };

        private static readonly string[] _genreSuffixes = {
            "Anthems", "Classics", "Hits", "Favorites", "Essentials", "Collection",
            "Masterpieces", "Legends", "Icons", "Heroes", "Stars", "Gems", "Treasures",
            "Discoveries", "Underground", "Mainstream", "Rarities", "Deep Cuts", "B-Sides"
        };

        private static readonly string[] _creativeWords = {
            "Stellar", "Lunar", "Solar", "Cosmic", "Galactic", "Nebula", "Orbit", "Gravity",
            "Phoenix", "Dragon", "Thunder", "Lightning", "Storm", "Rain", "Snow", "Fire",
            "Ice", "Crystal", "Diamond", "Pearl", "Ruby", "Emerald", "Sapphire", "Gold",
            "Silver", "Platinum", "Bronze", "Copper", "Steel", "Iron", "Titanium", "Chrome"
        };

        #endregion

        /// <summary>
        /// Generates a completely random playlist name
        /// </summary>
        /// <returns>A randomly generated playlist name</returns>
        public static string GenerateRandomName()
        {
            try
            {
                var strategies = new Func<string>[]
                {
                    GenerateMoodBasedName,
                    GenerateTimeBasedName,
                    GenerateGenreBasedName,
                    GenerateCreativeName
                };

                var selectedStrategy = strategies[_random.Next(strategies.Length)];
                var name = selectedStrategy();
                
                Logger.Debug("PlaylistNameGenerator", $"Generated random playlist name: {name}");
                return name;
            }
            catch (Exception ex)
            {
                Logger.Error("PlaylistNameGenerator", ex, "Error generating random playlist name");
                return "My Playlist"; // Fallback name
            }
        }

        /// <summary>
        /// Generates a playlist name based on a specific theme
        /// </summary>
        /// <param name="theme">The theme for the playlist (mood, time, genre, creative)</param>
        /// <returns>A themed playlist name</returns>
        public static string GenerateNameByTheme(PlaylistTheme theme)
        {
            try
            {
                string name = theme switch
                {
                    PlaylistTheme.Mood => GenerateMoodBasedName(),
                    PlaylistTheme.Time => GenerateTimeBasedName(),
                    PlaylistTheme.Genre => GenerateGenreBasedName(),
                    PlaylistTheme.Creative => GenerateCreativeName(),
                    _ => GenerateRandomName()
                };

                Logger.Debug("PlaylistNameGenerator", $"Generated {theme} themed playlist name: {name}");
                return name;
            }
            catch (Exception ex)
            {
                Logger.Error("PlaylistNameGenerator", ex, $"Error generating {theme} themed playlist name");
                return "My Playlist"; // Fallback name
            }
        }

        /// <summary>
        /// Generates multiple playlist name options
        /// </summary>
        /// <param name="count">Number of names to generate</param>
        /// <param name="theme">Optional theme to use for all names</param>
        /// <returns>List of generated playlist names</returns>
        public static List<string> GenerateMultipleNames(int count, PlaylistTheme? theme = null)
        {
            try
            {
                var names = new List<string>();
                var usedNames = new HashSet<string>();

                for (int i = 0; i < count && names.Count < count * 2; i++) // Prevent infinite loop
                {
                    string name = theme.HasValue ? GenerateNameByTheme(theme.Value) : GenerateRandomName();
                    
                    if (usedNames.Add(name)) // Add returns true if item was not already in set
                    {
                        names.Add(name);
                    }
                }

                Logger.Debug("PlaylistNameGenerator", $"Generated {names.Count} playlist names");
                return names;
            }
            catch (Exception ex)
            {
                Logger.Error("PlaylistNameGenerator", ex, "Error generating multiple playlist names");
                return new List<string> { "My Playlist" }; // Fallback
            }
        }

        #region Private Generation Methods

        private static string GenerateMoodBasedName()
        {
            var adjective = _moodAdjectives[_random.Next(_moodAdjectives.Length)];
            var noun = _moodNouns[_random.Next(_moodNouns.Length)];
            return $"{adjective} {noun}";
        }

        private static string GenerateTimeBasedName()
        {
            var prefix = _timeBasedPrefixes[_random.Next(_timeBasedPrefixes.Length)];
            var suffix = _timeBasedSuffixes[_random.Next(_timeBasedSuffixes.Length)];
            return $"{prefix} {suffix}";
        }

        private static string GenerateGenreBasedName()
        {
            var descriptor = _genreDescriptors[_random.Next(_genreDescriptors.Length)];
            var suffix = _genreSuffixes[_random.Next(_genreSuffixes.Length)];
            return $"{descriptor} {suffix}";
        }

        private static string GenerateCreativeName()
        {
            var word1 = _creativeWords[_random.Next(_creativeWords.Length)];
            var word2 = _moodNouns[_random.Next(_moodNouns.Length)];
            return $"{word1} {word2}";
        }

        #endregion
    }

    /// <summary>
    /// Themes for playlist name generation
    /// </summary>
    public enum PlaylistTheme
    {
        Mood,
        Time,
        Genre,
        Creative
    }
}
