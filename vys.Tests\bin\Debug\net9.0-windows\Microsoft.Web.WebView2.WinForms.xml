<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Web.WebView2.WinForms</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Web.WebView2.WinForms.CoreWebView2CreationProperties">
            <summary>
            This class is a bundle of the most common parameters used to create <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Environment"/> and <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Controller"/> instances.
            Its main purpose is to be set to <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CreationProperties"/> in order to customize the environment and/or controller used by a <see cref="T:Microsoft.Web.WebView2.WinForms.WebView2"/> during implicit initialization.
            </summary>
            <remarks>
            This class isn't intended to contain all possible environment or controller customization options.
            If you need complete control over the environment and/or controller used by a WebView2 control then you'll need to initialize the control explicitly by
            creating your own environment (with <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateAsync(System.String,System.String,Microsoft.Web.WebView2.Core.CoreWebView2EnvironmentOptions)"/>) and/or controller options (with <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateCoreWebView2ControllerOptions"/>) and passing them to <see cref="M:Microsoft.Web.WebView2.WinForms.WebView2.EnsureCoreWebView2Async(Microsoft.Web.WebView2.Core.CoreWebView2Environment,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)"/>
            *before* you set the <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.Source"/> property to anything.
            See the <see cref="T:Microsoft.Web.WebView2.WinForms.WebView2"/> class documentation for an initialization overview.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.WinForms.CoreWebView2CreationProperties.#ctor">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.Web.WebView2.WinForms.CoreWebView2CreationProperties"/> with default data for all properties.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.WinForms.CoreWebView2CreationProperties.BrowserExecutableFolder">
            <summary>
            Gets or sets the value to pass as the browserExecutableFolder parameter of <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateAsync(System.String,System.String,Microsoft.Web.WebView2.Core.CoreWebView2EnvironmentOptions)"/> when creating an environment with this instance.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.WinForms.CoreWebView2CreationProperties.UserDataFolder">
            <summary>
            Gets or sets the value to pass as the userDataFolder parameter of <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateAsync(System.String,System.String,Microsoft.Web.WebView2.Core.CoreWebView2EnvironmentOptions)"/> when creating an environment with this instance.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.WinForms.CoreWebView2CreationProperties.Language">
            <summary>
            Gets or sets the value to use for the Language property of the CoreWebView2EnvironmentOptions parameter passed to <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateAsync(System.String,System.String,Microsoft.Web.WebView2.Core.CoreWebView2EnvironmentOptions)"/> when creating an environment with this instance.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.WinForms.CoreWebView2CreationProperties.ProfileName">
            <summary>
            Gets or sets the value to use for the ProfileName property of the CoreWebView2ControllerOptions parameter passed to CreateCoreWebView2ControllerWithOptionsAsync when creating an controller with this instance.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.WinForms.CoreWebView2CreationProperties.AdditionalBrowserArguments">
            <summary>
            Gets or sets the value to pass as the AdditionalBrowserArguments parameter of <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2EnvironmentOptions"/> which is passed to <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateAsync(System.String,System.String,Microsoft.Web.WebView2.Core.CoreWebView2EnvironmentOptions)"/> when creating an environment with this instance.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.WinForms.CoreWebView2CreationProperties.IsInPrivateModeEnabled">
            <summary>
            Gets or sets the value to use for the IsInPrivateModeEnabled property of the CoreWebView2ControllerOptions parameter passed to CreateCoreWebView2ControllerWithOptionsAsync when creating an controller with this instance.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.WinForms.CoreWebView2CreationProperties.CreateEnvironmentAsync">
            <summary>
            Create a <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Environment"/> using the current values of this instance's properties.
            </summary>
            <returns>A task which will provide the created environment on completion, or null if no environment-related options are set.</returns>
            <remarks>
            As long as no other properties on this instance are changed, repeated calls to this method will return the same task/environment as earlier calls.
            If some other property is changed then the next call to this method will return a different task/environment.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.WinForms.CoreWebView2CreationProperties.CreateCoreWebView2ControllerOptions(Microsoft.Web.WebView2.Core.CoreWebView2Environment)">
            <summary>
            Creates a <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions"/> using the current values of this instance's properties.
            </summary>
            <returns>A <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions"/> object or null if no controller-related properties are set.</returns>
            <exception cref="T:System.NullReferenceException">Thrown if the parameter environment is null.</exception>
        </member>
        <member name="T:Microsoft.Web.WebView2.WinForms.WebView2">
            <summary>
            Control to embed WebView2 in WinForms.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.WinForms.WebView2.#ctor">
             <summary>
             Create a new WebView2 WinForms control.
             After construction the <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CoreWebView2"/> property is <c>null</c>.
             Call <see cref="M:Microsoft.Web.WebView2.WinForms.WebView2.EnsureCoreWebView2Async(Microsoft.Web.WebView2.Core.CoreWebView2Environment,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)"/> to initialize the underlying <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2"/>.
             </summary>
             <remarks>
             This control is effectively a wrapper around the WebView2 COM API, which you can find documentation for here: https://aka.ms/webview2
             You can directly access the underlying ICoreWebView2 interface and all of its functionality by accessing the <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CoreWebView2"/> property.
             Some of the most common COM functionality is also accessible directly through wrapper methods/properties/events on the control.
            
             Upon creation, the control's CoreWebView2 property will be null.
             This is because creating the CoreWebView2 is an expensive operation which involves things like launching Edge browser processes.
             There are two ways to cause the CoreWebView2 to be created:
             1) Call the <see cref="M:Microsoft.Web.WebView2.WinForms.WebView2.EnsureCoreWebView2Async(Microsoft.Web.WebView2.Core.CoreWebView2Environment,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)"/> method.  This is referred to as explicit initialization.
             2) Set the <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.Source"/> property. This is referred to as implicit initialization.
             Either option will start initialization in the background and return back to the caller without waiting for it to finish.
             To specify options regarding the initialization process, either pass your own <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Environment"/> to EnsureCoreWebView2Async or set the control's <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CreationProperties"/> property prior to initialization.
            
             When initialization has finished (regardless of how it was triggered) then the following things will occur, in this order:
             1) The control's <see cref="E:Microsoft.Web.WebView2.WinForms.WebView2.CoreWebView2InitializationCompleted"/> event will be invoked.  If you need to perform one time setup operations on the CoreWebView2 prior to its use then you should do so in a handler for that event.
             2) If a Uri has been set to the <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.Source"/> property then the control will start navigating to it in the background (i.e. these steps will continue without waiting for the navigation to finish).
             3) The Task returned from <see cref="M:Microsoft.Web.WebView2.WinForms.WebView2.EnsureCoreWebView2Async(Microsoft.Web.WebView2.Core.CoreWebView2Environment,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)"/> will complete.
            
             For more details about any of the methods/properties/events involved in the initialization process, see its specific documentation.
            
             Accelerator key presses (e.g. Ctrl+P) that occur within the control will
             fire standard key press events such as OnKeyDown. You can suppress the
             control's default implementation of an accelerator key press (e.g.
             printing, in the case of Ctrl+P) by setting the Handled property of its
             EventArgs to true. Also note that the underlying browser process is
             blocked while these handlers execute, so:
             <list type="number">
             <item>
             You should avoid doing a lot of work in these handlers.
             </item>
             <item>
             Some of the WebView2 and CoreWebView2 APIs may throw errors if
             invoked within these handlers due to being unable to communicate with
             the browser process.
             </item>
             </list>
             If you need to do a lot of work and/or invoke WebView2 APIs in response to
             accelerator keys then consider kicking off a background task or queuing
             the work for later execution on the UI thread.
             </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.WinForms.WebView2.Dispose(System.Boolean)">
            <summary>
            Cleans up any resources being used.
            </summary>
            <param name="disposing"><c>true</c> if managed resources should be disposed; otherwise, <c>false</c>.</param>
        </member>
        <member name="M:Microsoft.Web.WebView2.WinForms.WebView2.OnPaint(System.Windows.Forms.PaintEventArgs)">
            <summary>
            Overrides the base OnPaint event to have custom actions
            in designer mode
            </summary>
            <param name="e">The graphics devices which is the source</param>
        </member>
        <member name="M:Microsoft.Web.WebView2.WinForms.WebView2.WndProc(System.Windows.Forms.Message@)">
            <summary>
            Overrides the base WndProc events to handle specific window messages.
            </summary>
            <param name="m">The Message object containing the HWND window message and parameters</param>
        </member>
        <member name="P:Microsoft.Web.WebView2.WinForms.WebView2.CreationProperties">
            <summary>
            Gets or sets a bag of options which are used during initialization of the control's <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CoreWebView2"/>.
            This property cannot be modified (an exception will be thrown) after initialization of the control's CoreWebView2 has started.
            </summary>
            <exception cref="T:System.InvalidOperationException">Thrown if initialization of the control's CoreWebView2 has already started.</exception>
        </member>
        <member name="M:Microsoft.Web.WebView2.WinForms.WebView2.EnsureCoreWebView2Async(Microsoft.Web.WebView2.Core.CoreWebView2Environment,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)">
            <summary>
            Explicitly trigger initialization of the control's <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CoreWebView2"/>.
            </summary>
            <param name="environment">
            A pre-created <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Environment"/> that should be used to create the <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CoreWebView2"/>.
            Creating your own environment gives you control over several options that affect how the <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CoreWebView2"/> is initialized.
            If you pass <c>null</c> (the default value) then a default environment will be created and used automatically.
            </param>
            <param name="controllerOptions">
            A pre-created <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions"/> that should be used to create the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2"/>.
            Creating your own controller options gives you control over several options that affect how the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2"/> is initialized.
            If you pass a controllerOptions to this method then it will override any settings specified on the <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CreationProperties"/> property.
            If you pass <c>null</c> (the default value) and no value has been set to <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CreationProperties"/> then a default controllerOptions will be created and used automatically.
            </param>
            <returns>
            A Task that represents the background initialization process.
            When the task completes then the <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CoreWebView2"/> property will be available for use (i.e. non-null).
            Note that the control's <see cref="E:Microsoft.Web.WebView2.WinForms.WebView2.CoreWebView2InitializationCompleted"/> event will be invoked before the task completes
            or on exceptions.
            </returns>
            <remarks>
            Unless previous initialization has already failed, calling this method additional times with the same parameter will have no effect (any specified environment is ignored) and return the same Task as the first call.
            Unless previous initialization has already failed, calling this method after initialization has been implicitly triggered by setting the <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.Source"/> property will have no effect if no environment is given
            and simply return a Task representing that initialization already in progress.
            Unless previous initialization has already failed, calling this method with a different environment after initialization has begun will result in an <see cref="T:System.ArgumentException"/>. For example, this can happen if you begin initialization
            by setting the <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.Source"/> property and then call this method with a new environment, if you begin initialization with <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CreationProperties"/> and then call this method with a new
            environment, or if you begin initialization with one environment and then call this method with no environment specified.
            When this method is called after previous initialization has failed, it will trigger initialization of the control's <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CoreWebView2"/> again.
            Note that even though this method is asynchronous and returns a Task, it still must be called on the UI thread like most public functionality of most UI controls.
            <para>
            The following summarizes the possible error values and a description of why these errors occur.
            <list type="table">
            <listheader>
            <description>Error Value</description>
            <description>Description</description>
            </listheader>
            <item>
            <description><c>HRESULT_FROM_WIN32(ERROR_NOT_SUPPORTED)</c></description>
            <description>*\\Edge\\Application* path used in browserExecutableFolder.</description>
            </item>
            <item>
            <description><c>HRESULT_FROM_WIN32(ERROR_INVALID_STATE)</c></description>
            <description>Specified options do not match the options of the WebViews that are currently running in the shared browser process.</description>
            </item>
            <item>
            <description><c>HRESULT_FROM_WIN32(ERROR_INVALID_WINDOW_HANDLE)</c></description>
            <description>WebView2 Initialization failed due to an invalid host HWND parentWindow.</description>
            </item>
            <item>
            <description><c>HRESULT_FROM_WIN32(ERROR_DISK_FULL)</c></description>
            <description>WebView2 Initialization failed due to reaching the maximum number of installed runtime versions.</description>
            </item>
            <item>
            <description><c>HRESULT_FROM_WIN32(ERROR_PRODUCT_UNINSTALLED</c></description>
            <description>If the Webview depends upon an installed WebView2 Runtime version and it is uninstalled.</description>
            </item>
            <item>
            <description><c>HRESULT_FROM_WIN32(ERROR_FILE_NOT_FOUND)</c></description>
            <description>Could not find Edge installation.</description>
            </item>
            <item>
            <description><c>HRESULT_FROM_WIN32(ERROR_FILE_EXISTS)</c></description>
            <description>User data folder cannot be created because a file with the same name already exists.</description>
            </item>
            <item>
            <description><c>E_ACCESSDENIED</c></description>
            <description>Unable to create user data folder, Access Denied.</description>
            </item>
            <item>
            <description><c>E_FAIL</c></description>
            <description>Edge runtime unable to start.</description>
            </item>
            </list>
            </para>
            </remarks>
            <exception cref="T:System.ArgumentException">
            Thrown if this method is called with a different environment than when it was initialized. See Remarks for more info.
            </exception>
            <exception cref="T:System.InvalidOperationException">
            Thrown if this instance of <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CoreWebView2"/> is already disposed, or if the calling thread isn't the thread which created this object (usually the UI thread). See <see cref="P:System.Windows.Forms.Control.InvokeRequired"/> for more info.
            May also be thrown if the browser process has crashed unexpectedly and left the control in an invalid state. We are considering throwing a different type of exception for this case in the future.
            </exception>
        </member>
        <member name="M:Microsoft.Web.WebView2.WinForms.WebView2.EnsureCoreWebView2Async(Microsoft.Web.WebView2.Core.CoreWebView2Environment)">
            <summary>
            Explicitly trigger initialization of the control's <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CoreWebView2"/>.
            </summary>
            <param name="environment">
            A pre-created <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Environment"/> that should be used to create the <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CoreWebView2"/>.
            Creating your own environment gives you control over several options that affect how the <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CoreWebView2"/> is initialized.
            If you pass <c>null</c> then a default environment will be created and used automatically.
            </param>
            <returns>
            A Task that represents the background initialization process.
            When the task completes then the <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CoreWebView2"/> property will be available for use (i.e. non-null).
            Note that the control's <see cref="E:Microsoft.Web.WebView2.WinForms.WebView2.CoreWebView2InitializationCompleted"/> event will be invoked before the task completes
            or on exceptions.
            </returns>
            <remarks>
            Unless previous initialization has already failed, calling this method additional times with the same parameter will have no effect (any specified environment is ignored) and return the same Task as the first call.
            Unless previous initialization has already failed, calling this method after initialization has been implicitly triggered by setting the <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.Source"/> property will have no effect if no environment is given
            and simply return a Task representing that initialization already in progress.
            Unless previous initialization has already failed, calling this method with a different environment after initialization has begun will result in an <see cref="T:System.ArgumentException"/>. For example, this can happen if you begin initialization
            by setting the <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.Source"/> property and then call this method with a new environment, if you begin initialization with <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CreationProperties"/> and then call this method with a new
            environment, or if you begin initialization with one environment and then call this method with no environment specified.
            When this method is called after previous initialization has failed, it will trigger initialization of the control's <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CoreWebView2"/> again.
            Note that even though this method is asynchronous and returns a Task, it still must be called on the UI thread like most public functionality of most UI controls.
            </remarks>
            <exception cref="T:System.ArgumentException">
            Thrown if this method is called with a different environment than when it was initialized. See Remarks for more info.
            </exception>
            <exception cref="T:System.InvalidOperationException">
            Thrown if this instance of <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CoreWebView2"/> is already disposed, or if the calling thread isn't the thread which created this object (usually the UI thread). See <see cref="P:System.Windows.Forms.Control.InvokeRequired"/> for more info.
            May also be thrown if the browser process has crashed unexpectedly and left the control in an invalid state. We are considering throwing a different type of exception for this case in the future.
            </exception>
        </member>
        <member name="M:Microsoft.Web.WebView2.WinForms.WebView2.InitCoreWebView2Async(Microsoft.Web.WebView2.Core.CoreWebView2Environment,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)">
            <summary>
            This is the private function which implements the actual background initialization task.
            Cannot be called if the control is already initialized or has been disposed.
            </summary>
            <param name="environment">
            The environment to use to create the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Controller"/>.
            If that is null then a default environment is created with <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateAsync(System.String,System.String,Microsoft.Web.WebView2.Core.CoreWebView2EnvironmentOptions)"/> and its default parameters.
            </param>
            <param name="controllerOptions">
            The controllerOptions to use to create the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Controller"/>.
            If that is null then a default controllerOptions is created with its default parameters.
            </param>
            <returns>A task representing the background initialization process.</returns>
            <remarks>All the event handlers added here need to be removed in <see cref="M:Microsoft.Web.WebView2.WinForms.WebView2.Dispose(System.Boolean)"/>.</remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.WinForms.WebView2.CreateParams">
            <summary>
            Protected CreateParams property. Used to set custom window styles to the forms HWND.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.WinForms.WebView2.OnVisibleChanged(System.EventArgs)">
            <summary>
            Protected VisibilityChanged handler.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.WinForms.WebView2.OnSizeChanged(System.EventArgs)">
            <summary>
            Protected SizeChanged handler.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.WinForms.WebView2.Select(System.Boolean,System.Boolean)">
            <summary>
            Protected Select method: override this to capture tab direction when WebView control is activated
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.WinForms.WebView2.OnGotFocus(System.EventArgs)">
            <summary>
            Protected OnGotFocus handler.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.WinForms.WebView2.OnParentChanged(System.EventArgs)">
            <summary>
            Protected OnParentChanged handler.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.WinForms.WebView2.IsInitialized">
            <summary>
            True if initialization finished successfully and the control is not disposed yet.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.WinForms.WebView2.GetSitedParentSite(System.Windows.Forms.Control)">
            <summary>
            Recursive retrieval of the parent control
            </summary>
            <param name="control">The control to get the parent for</param>
            <returns>The root parent control</returns>
        </member>
        <member name="P:Microsoft.Web.WebView2.WinForms.WebView2.CoreWebView2">
            <summary>
            The underlying CoreWebView2. Use this property to perform more operations on the WebView2 content than is exposed
            on the WebView2. This value is null until it is initialized and the object itself has undefined behaviour once the control is disposed.
            You can force the underlying CoreWebView2 to
            initialize via the <see cref="M:Microsoft.Web.WebView2.WinForms.WebView2.EnsureCoreWebView2Async(Microsoft.Web.WebView2.Core.CoreWebView2Environment,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)"/> method.
            </summary>
            <exception cref="T:System.InvalidOperationException">Thrown if the calling thread isn't the thread which created this object (usually the UI thread). See <see cref="P:System.Windows.Forms.Control.InvokeRequired"/> for more info.</exception>
        </member>
        <member name="P:Microsoft.Web.WebView2.WinForms.WebView2.ZoomFactor">
            <summary>
            The zoom factor for the WebView.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.WinForms.WebView2.AllowExternalDrop">
            <summary>
            Enable/disable external drop.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.WinForms.WebView2.Source">
            <summary>
            The Source property is the URI of the top level document of the
            WebView2. Setting the Source is equivalent to calling <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.Navigate(System.String)"/>.
            Setting the Source will trigger initialization of the <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CoreWebView2"/>, if not already initialized.
            The default value of Source is <c>null</c>, indicating that the <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CoreWebView2"/> is not yet initialized.
            </summary>
            <exception cref="T:System.ArgumentException">Specified value is not an absolute <see cref="T:System.Uri"/>.</exception>
            <exception cref="T:System.NotImplementedException">Specified value is <c>null</c> and the control is initialized.</exception>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.Navigate(System.String)"/>
        </member>
        <member name="P:Microsoft.Web.WebView2.WinForms.WebView2.CanGoForward">
            <summary>
            Returns true if the webview can navigate to a next page in the
            navigation history via the <see cref="M:Microsoft.Web.WebView2.WinForms.WebView2.GoForward"/> method.
            This is equivalent to the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2.CanGoForward"/>.
            If the underlying <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CoreWebView2"/> is not yet initialized, this property is <c>false</c>.
            </summary>
            <seealso cref="P:Microsoft.Web.WebView2.Core.CoreWebView2.CanGoForward"/>
        </member>
        <member name="P:Microsoft.Web.WebView2.WinForms.WebView2.CanGoBack">
            <summary>
            Returns <c>true</c> if the webview can navigate to a previous page in the
            navigation history via the <see cref="M:Microsoft.Web.WebView2.WinForms.WebView2.GoBack"/> method.
            This is equivalent to the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2.CanGoBack"/>.
            If the underlying <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CoreWebView2"/> is not yet initialized, this property is <c>false</c>.
            </summary>
            <seealso cref="P:Microsoft.Web.WebView2.Core.CoreWebView2.CanGoBack"/>
        </member>
        <member name="P:Microsoft.Web.WebView2.WinForms.WebView2.DefaultBackgroundColor">
            <summary>
            The default background color for the WebView.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.WinForms.WebView2.ExecuteScriptAsync(System.String)">
            <summary>
            Executes the provided script in the top level document of the <see cref="T:Microsoft.Web.WebView2.WinForms.WebView2"/>.
            This is equivalent to <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.ExecuteScriptAsync(System.String)"/>.
            </summary>
            <exception cref="T:System.InvalidOperationException">The underlying <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CoreWebView2"/> is not yet initialized.</exception>
            <exception cref="T:System.InvalidOperationException">Thrown when browser process has unexpectedly and left this control in an invalid state. We are considering throwing a different type of exception for this case in the future.</exception>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.ExecuteScriptAsync(System.String)"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.WinForms.WebView2.Reload">
            <summary>
            Reloads the top level document of the <see cref="T:Microsoft.Web.WebView2.WinForms.WebView2"/>.
            This is equivalent to <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.Reload"/>.
            </summary>
            <exception cref="T:System.InvalidOperationException">The underlying <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CoreWebView2"/> is not yet initialized.</exception>
            <exception cref="T:System.InvalidOperationException">Thrown when browser process has unexpectedly and left this control in an invalid state. We are considering throwing a different type of exception for this case in the future.</exception>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.Reload"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.WinForms.WebView2.GoForward">
            <summary>
            Navigates to the next page in navigation history.
            This is equivalent to <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.GoForward"/>.
            If the underlying <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CoreWebView2"/> is not yet initialized, this method does nothing.
            </summary>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.GoForward"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.WinForms.WebView2.GoBack">
            <summary>
            Navigates to the previous page in navigation history.
            This is equivalent to <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.GoBack"/>.
            If the underlying <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CoreWebView2"/> is not yet initialized, this method does nothing.
            </summary>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.GoBack"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.WinForms.WebView2.NavigateToString(System.String)">
            <summary>
            Renders the provided HTML as the top level document of the <see cref="T:Microsoft.Web.WebView2.WinForms.WebView2"/>.
            This is equivalent to <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.NavigateToString(System.String)"/>.
            </summary>
            <exception cref="T:System.InvalidOperationException">The underlying <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CoreWebView2"/> is not yet initialized.</exception>
            <exception cref="T:System.InvalidOperationException">Thrown when browser process has unexpectedly and left this control in an invalid state. We are considering throwing a different type of exception for this case in the future.</exception>
            <remarks>The <c>htmlContent</c> parameter may not be larger than 2 MB (2 * 1024 * 1024 bytes) in total size. The origin of the new page is <c>about:blank</c>.</remarks>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.NavigateToString(System.String)"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.WinForms.WebView2.Stop">
            <summary>
            Stops any in progress navigation in the <see cref="T:Microsoft.Web.WebView2.WinForms.WebView2"/>.
            This is equivalent to <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.Stop"/>.
            If the underlying <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CoreWebView2"/> is not yet initialized, this method does nothing.
            </summary>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.Stop"/>
        </member>
        <member name="E:Microsoft.Web.WebView2.WinForms.WebView2.CoreWebView2InitializationCompleted">
            <summary>
            This event is triggered either 1) when the control's <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.CoreWebView2"/> has finished being initialized (regardless of how it was triggered or whether it succeeded) but before it is used for anything
            OR 2) the initialization failed.
            You should handle this event if you need to perform one time setup operations on the CoreWebView2 which you want to affect all of its usages
            (e.g. adding event handlers, configuring settings, installing document creation scripts, adding host objects).
            </summary>
            <remarks>
            This sender will be the WebView2 control, whose CoreWebView2 property will now be valid (i.e. non-null) for the first time
            if <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2InitializationCompletedEventArgs.IsSuccess"/> is true.
            Unlikely this event can fire second time (after reporting initialization success first)
            if the initialization is followed by navigation which fails.
            </remarks>
        </member>
        <member name="E:Microsoft.Web.WebView2.WinForms.WebView2.NavigationStarting">
            <summary>
            NavigationStarting dispatches before a new navigate starts for the top
            level document of the <see cref="T:Microsoft.Web.WebView2.WinForms.WebView2"/>.
            This is equivalent to the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationStarting"/> event.
            </summary>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationStarting"/>
        </member>
        <member name="E:Microsoft.Web.WebView2.WinForms.WebView2.NavigationCompleted">
            <summary>
            NavigationCompleted dispatches after a navigate of the top level
            document completes rendering either successfully or not.
            This is equivalent to the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationCompleted"/> event.
            </summary>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationCompleted"/>
        </member>
        <member name="E:Microsoft.Web.WebView2.WinForms.WebView2.WebMessageReceived">
            <summary>
            WebMessageReceived dispatches after web content sends a message to the
            app host via <c>chrome.webview.postMessage</c>.
            This is equivalent to the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebMessageReceived"/> event.
            </summary>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebMessageReceived"/>
        </member>
        <member name="E:Microsoft.Web.WebView2.WinForms.WebView2.SourceChanged">
            <summary>
            SourceChanged dispatches after the <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.Source"/> property changes. This may happen
            during a navigation or if otherwise the script in the page changes the
            URI of the document.
            This is equivalent to the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.SourceChanged"/> event.
            </summary>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.SourceChanged"/>
        </member>
        <member name="E:Microsoft.Web.WebView2.WinForms.WebView2.ContentLoading">
            <summary>
            ContentLoading dispatches after a navigation begins to a new URI and the
            content of that URI begins to render.
            This is equivalent to the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ContentLoading"/> event.
            </summary>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ContentLoading"/>
        </member>
        <member name="E:Microsoft.Web.WebView2.WinForms.WebView2.ZoomFactorChanged">
            <summary>
            ZoomFactorChanged dispatches when the <see cref="P:Microsoft.Web.WebView2.WinForms.WebView2.ZoomFactor"/> property changes.
            This is equivalent to the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Controller.ZoomFactorChanged"/> event.
            </summary>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Controller.ZoomFactorChanged"/>
        </member>
        <member name="F:Microsoft.Web.WebView2.WinForms.WebView2.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.WinForms.WebView2.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify 
            the contents of this method with the code editor.
            </summary>
        </member>
    </members>
</doc>
