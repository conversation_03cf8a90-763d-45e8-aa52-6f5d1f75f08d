{"net": {"http_server_properties": {"broken_alternative_services": [{"anonymization": ["GAAAABMAAABodHRwczovL3Nwb3RpZnkuY29tAA==", false, 0], "broken_count": 1, "host": "o22381.ingest.sentry.io", "port": 443, "protocol_str": "quic"}], "servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", true, 0], "server": "https://accounts.youtube.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://www.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "server": "https://accounts.youtube.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABEAAABodHRwczovL2dvb2dsZS5wdAAAAA==", false, 0], "server": "https://accounts.google.pt", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 21659}, "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 19649}, "server": "https://fonts.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 35950}, "server": "https://play.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3Nwb3RpZnkuY29tAA==", false, 0], "server": "https://image-cdn-ak.spotifycdn.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3Nwb3RpZnkuY29tAA==", false, 0], "server": "https://scontent-lhr6-1.xx.fbcdn.net", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3Nwb3RpZnkuY29tAA==", false, 0], "server": "https://canvaz.scdn.co", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3Nwb3RpZnkuY29tAA==", false, 0], "server": "https://daylist.spotifycdn.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397754529241401", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL2ZhY2Vib29rLmNvbQ==", false, 0], "server": "https://facebook.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397754529462296", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL2ZhY2Vib29rLmNvbQ==", false, 0], "server": "https://fbcdn.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397754529609232", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL2ZhY2Vib29rLmNvbQ==", false, 0], "server": "https://fbsbx.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397754529752482", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL2ZhY2Vib29rLmNvbQ==", false, 0], "server": "https://connect.facebook.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397754529781775", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL2ZhY2Vib29rLmNvbQ==", false, 0], "network_stats": {"srtt": 24523}, "server": "https://static.xx.fbcdn.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397754530332980", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL2ZhY2Vib29rLmNvbQ==", false, 0], "network_stats": {"srtt": 28724}, "server": "https://www.facebook.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13400267546279192", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3Nwb3RpZnkuY29tAA==", false, 0], "network_stats": {"srtt": 17271}, "server": "https://www.googleoptimize.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3Nwb3RpZnkuY29tAA==", false, 0], "server": "https://i.scdn.co", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3Nwb3RpZnkuY29tAA==", false, 0], "server": "https://cdn.cookielaw.org", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13400176612089166", "port": 443, "protocol_str": "quic"}, {"advertised_alpns": ["h3"], "expiration": "13400176612089166", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3Nwb3RpZnkuY29tAA==", false, 0], "network_stats": {"srtt": 21009}, "server": "https://api.spotify.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13400268082325525", "port": 443, "protocol_str": "quic"}, {"advertised_alpns": ["h3"], "expiration": "13400268082325526", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3Nwb3RpZnkuY29tAA==", false, 0], "network_stats": {"srtt": 21009}, "server": "https://clienttoken.spotify.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3Nwb3RpZnkuY29tAA==", false, 0], "server": "https://charts-images.scdn.co", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3Nwb3RpZnkuY29tAA==", false, 0], "server": "https://pickasso.spotifycdn.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL21pY3Jvc29mdC5jb20AAAA=", false, 0], "server": "https://edge.microsoft.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABkZXZ0b29sczovL2RldnRvb2xzAA==", false, 0], "server": "https://msedgedevtools.microsoft.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3Nwb3RpZnkuY29tAA==", false, 0], "server": "https://open.spotify.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3Nwb3RpZnkuY29tAA==", false, 0], "server": "https://o22381.ingest.sentry.io", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3Nwb3RpZnkuY29tAA==", false, 0], "server": "https://encore.scdn.co", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13400267867136193", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3Nwb3RpZnkuY29tAA==", false, 0], "network_stats": {"srtt": 21373}, "server": "https://www.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13400029958997356", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3Nwb3RpZnkuY29tAA==", true, 0], "network_stats": {"srtt": 17635}, "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399949669779400", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3Nwb3RpZnkuY29tAA==", true, 0], "server": "https://fonts.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13400268082461345", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3Nwb3RpZnkuY29tAA==", false, 0], "network_stats": {"srtt": 18207}, "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3Nwb3RpZnkuY29tAA==", false, 0], "network_stats": {"srtt": 21426}, "server": "https://spclient.wg.spotify.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 52839}, "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}, {"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3Nwb3RpZnkuY29tAA==", false, 0], "network_stats": {"srtt": 21270}, "server": "https://accounts.spotify.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}, {"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3Nwb3RpZnkuY29tAA==", false, 0], "network_stats": {"srtt": 21270}, "server": "https://www.spotify.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}, {"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3Nwb3RpZnkuY29tAA==", false, 0], "network_stats": {"srtt": 19923}, "server": "https://apresolve.spotify.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}, {"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3Nwb3RpZnkuY29tAA==", false, 0], "network_stats": {"srtt": 19923}, "server": "https://api-partner.spotify.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13400268103260776", "port": 443, "protocol_str": "quic"}, {"advertised_alpns": ["h3"], "expiration": "13400268103260776", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3Nwb3RpZnkuY29tAA==", false, 0], "network_stats": {"srtt": 19621}, "server": "https://gew1-spclient.spotify.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397762494958756", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3Nwb3RpZnkuY29tAA==", false, 0], "network_stats": {"srtt": 9774}, "server": "https://open.spotifycdn.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13400268101961966", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3Nwb3RpZnkuY29tAA==", true, 0], "network_stats": {"srtt": 19812}, "server": "https://www.google.com", "supports_spdy": true}], "supports_quic": {"address": "********", "used_quic": true}, "version": 5}, "network_qualities": {"CAASABiAgICA+P////8B": "4G", "CAESABiAgICA+P////8B": "4G", "CAYSABiAgICA+P////8B": "Offline"}}}