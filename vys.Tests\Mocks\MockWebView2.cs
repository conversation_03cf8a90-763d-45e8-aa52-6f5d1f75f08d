using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using vys.Interfaces;

namespace vys.Tests.Automation
{
    /// <summary>
    /// Mock implementation of IWebView2Wrapper for testing purposes
    /// </summary>
    public class MockWebView2 : IWebView2Wrapper, IDisposable
    {
        private readonly Queue<string> _scriptResponses;
        private string? _singleResponse;
        private Exception? _scriptError;
        private string _currentSource;

        public MockWebView2()
        {
            _scriptResponses = new Queue<string>();
            _currentSource = "https://open.spotify.com/";
        }

        /// <summary>
        /// Sets a single response for all ExecuteScriptAsync calls
        /// </summary>
        public void SetScriptResponse(string response)
        {
            _singleResponse = response;
            _scriptError = null;
        }

        /// <summary>
        /// Sets multiple responses that will be returned in sequence
        /// </summary>
        public void SetMultipleScriptResponses(string[] responses)
        {
            _scriptResponses.Clear();
            foreach (var response in responses)
            {
                _scriptResponses.Enqueue(response);
            }
            _singleResponse = null;
            _scriptError = null;
        }

        /// <summary>
        /// Sets an error to be thrown when ExecuteScriptAsync is called
        /// </summary>
        public void SetScriptError(Exception error)
        {
            _scriptError = error;
            _singleResponse = null;
        }

        /// <summary>
        /// Mock implementation of ExecuteScriptAsync
        /// </summary>
        public async Task<string> ExecuteScriptAsync(string javaScript)
        {
            // Simulate async delay
            await Task.Delay(10);

            // Throw error if configured
            if (_scriptError != null)
            {
                throw _scriptError;
            }

            // Return queued response if available
            if (_scriptResponses.Count > 0)
            {
                return _scriptResponses.Dequeue();
            }

            // Return single response if configured
            if (_singleResponse != null)
            {
                return _singleResponse;
            }

            // Default responses based on script content
            if (javaScript.Contains("toGetStartedFound"))
            {
                // Dashboard test script
                return @"{
                    ""toGetStartedFound"": false,
                    ""toGetStartedElement"": null,
                    ""hasMainContent"": true,
                    ""hasNavigation"": true,
                    ""currentUrl"": ""https://open.spotify.com/""
                }";
            }

            if (javaScript.Contains("homeRelatedButtons"))
            {
                // Debug script for buttons
                return @"{
                    ""totalButtons"": 0,
                    ""homeRelatedButtons"": []
                }";
            }

            if (javaScript.Contains("success"))
            {
                // Home button click script
                return @"{""success"": false, ""error"": ""No home button found""}";
            }

            // Default boolean response
            return "false";
        }

        /// <summary>
        /// Mock implementation of Navigate
        /// </summary>
        public void Navigate(string uri)
        {
            _currentSource = uri;
        }

        /// <summary>
        /// Mock implementation of ReloadAsync
        /// </summary>
        public async Task ReloadAsync()
        {
            // Mock implementation - just wait a bit to simulate reload
            await Task.Delay(100);
        }

        /// <summary>
        /// Mock implementation of Source property
        /// </summary>
        public string Source => _currentSource;

        /// <summary>
        /// Dispose method for cleanup
        /// </summary>
        public void Dispose()
        {
            // Cleanup if needed
        }
    }
}
