# VYS Browser Tests

This project contains unit tests for the VYS Browser Spotify automation functionality.

## Project Structure

```
vys.Tests/
├── Automation/
│   └── DashboardNavigationTests.cs    # Tests for dashboard navigation
├── Mocks/
│   └── MockWebView2.cs                # Mock WebView2 for testing
└── README.md                          # This file
```

## Test Coverage

### Dashboard Navigation Tests

The `DashboardNavigationTests` class contains comprehensive tests for the Spotify dashboard navigation functionality:

1. **TestDashboardNavigation_ShouldReturnTrue_WhenToGetStartedElementExists**
   - Tests successful dashboard detection when "To get you started" element is present
   - Verifies the test passes when the expected element is found

2. **TestDashboardNavigation_ShouldReturnFalse_WhenToGetStartedElementMissing**
   - Tests failed dashboard detection when "To get you started" element is missing
   - Verifies the test correctly fails when the element is not found

3. **TestGoToDashboard_ShouldNavigateAndVerify_WhenHomeButtonExists**
   - Tests the complete navigation workflow (navigate + verify)
   - Mocks successful home button detection and dashboard verification

4. **TestGoToDashboard_ShouldReturnFalse_WhenNavigationFails**
   - Tests the complete workflow when navigation fails
   - Verifies proper failure handling when home button is not found

5. **TestDashboardNavigation_ShouldHandleDifferentTextCasing**
   - Parameterized test for different text casing scenarios
   - Tests: "To get you started", "TO GET YOU STARTED", "to get you started"

6. **TestDashboardNavigation_ShouldHandleJavaScriptErrors**
   - Tests error handling when JavaScript execution fails
   - Verifies the method returns false instead of throwing exceptions

## Key Test Features

### Mock WebView2
- **Interface-based testing**: Uses `IWebView2Wrapper` interface for testability
- **Configurable responses**: Can set single responses, multiple responses, or errors
- **Realistic simulation**: Mimics actual WebView2 behavior for testing

### Test Scenarios
- **Success cases**: Dashboard elements found, navigation successful
- **Failure cases**: Elements missing, navigation failed, JavaScript errors
- **Edge cases**: Different text casing, error conditions
- **Integration tests**: Complete navigation + verification workflows

## Running Tests

### Command Line
```bash
# Run all tests
dotnet test vys.Tests

# Run with detailed output
dotnet test vys.Tests --verbosity normal

# Run specific test
dotnet test vys.Tests --filter "TestDashboardNavigation_ShouldReturnTrue_WhenToGetStartedElementExists"
```

### PowerShell Script
```powershell
# Run the provided test script
.\run-tests.ps1
```

### Visual Studio
- Open the solution in Visual Studio
- Use Test Explorer to run individual tests or all tests
- View detailed test output and debugging information

## Test Results

When all tests pass, you should see output similar to:
```
Test summary: total: 8, failed: 0, succeeded: 8, skipped: 0
✅ All tests passed!
🎉 Dashboard navigation functionality is working correctly!
```

## Test Architecture

### Dependency Injection
The tests use constructor injection to provide mock dependencies:
- `ITestOutputHelper` for test output
- `MockWebView2` for simulating WebView2 behavior

### Interface Segregation
- `IWebView2Wrapper` interface enables testing without real WebView2
- `WebView2Wrapper` provides production implementation
- `MockWebView2` provides test implementation

### Test Data Management
- Mock responses are configured per test
- Multiple response sequences supported for complex workflows
- Error simulation for exception handling tests

## Extending Tests

To add new tests:

1. **Create test method** with `[Fact]` or `[Theory]` attribute
2. **Configure mock** using `_mockWebView.SetScriptResponse()`
3. **Execute method** under test
4. **Assert results** using xUnit assertions
5. **Add output** using `_output.WriteLine()` for debugging

Example:
```csharp
[Fact]
public async Task MyNewTest()
{
    // Arrange
    _mockWebView.SetScriptResponse(@"{""success"": true}");
    
    // Act
    var result = await _spotifyAutomation.MyMethod();
    
    // Assert
    Assert.True(result);
}
```

## Dependencies

- **xUnit**: Testing framework
- **Microsoft.NET.Test.Sdk**: Test SDK
- **WebView2**: For interface definitions
- **VYS main project**: Reference to production code

The tests ensure the dashboard navigation functionality works correctly and can detect the presence of the "To get you started" element that indicates successful navigation to the Spotify dashboard.
