using System.Threading.Tasks;
using Microsoft.Web.WebView2.Core;
using vys.Interfaces;

namespace vys
{
    /// <summary>
    /// Wrapper for CoreWebView2 to enable testing
    /// </summary>
    public class WebView2Wrapper : IWebView2Wrapper
    {
        private readonly CoreWebView2 _coreWebView2;

        public WebView2Wrapper(CoreWebView2 coreWebView2)
        {
            _coreWebView2 = coreWebView2 ?? throw new ArgumentNullException(nameof(coreWebView2));
        }

        public async Task<string> ExecuteScriptAsync(string javaScript)
        {
            return await _coreWebView2.ExecuteScriptAsync(javaScript);
        }

        public void Navigate(string uri)
        {
            _coreWebView2.Navigate(uri);
        }

        public async Task ReloadAsync()
        {
            await Task.Run(() => _coreWebView2.Reload());
        }

        public string Source => _coreWebView2.Source;
    }
}
