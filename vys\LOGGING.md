# VYS Browser Logging System

## Overview

VYS Browser includes a configurable logging system that provides clean console output for end users while preserving detailed debugging information for developers and troubleshooting.

## Usage

### Production Mode (Default)
```bash
# Run with minimal logging (warnings and errors only)
dotnet run
# or
vys.exe
```

**Output Example:**
```
⚠️ [AudioManager] No audio sessions found for tracked processes
❌ [SpotifyLoginDetector] Failed to check login status
```

### Debug Mode
```bash
# Run with verbose logging and debug file
dotnet run --debug
# or
vys.exe --debug
# or
vys.exe -d
# or
vys.exe --verbose
# or
vys.exe -v
```

**Output Example:**
```
[2025-07-15 20:14:57.364] ℹ️ INFO [Logger] Logger initialized - Debug Mode: True, Log Level: Debug (Initialize:53)
[2025-07-15 20:14:57.373] ℹ️ INFO [Application] VYS Browser starting with WebView2... (OnStartup:21)
[2025-07-15 20:14:57.404] 🔍 DEBUG [MainWindow] Constructor starting... (.ctor:33)
[2025-07-15 20:14:57.586] 🔍 DEBUG [WindowsAudioApi] Audio device initialized for main process 12252 (vys) (InitializeAudioDevice:107)
```

### Help
```bash
# Show usage information
dotnet run -- --help
# or
vys.exe --help
# or
vys.exe -h
```

## Log Levels

### 🔍 Debug Level (Debug Mode Only)
- Detailed method entry/exit tracking
- Internal state changes and variable values
- Step-by-step operation flow
- Performance timing information
- Low-level API interactions

**Examples:**
- Constructor initialization steps
- Audio session enumeration details
- WebView2 process discovery
- Cookie validation steps

### ℹ️ Info Level (Debug Mode Only)
- Application lifecycle events
- Successful operation completions
- Configuration changes
- User action confirmations
- System state updates

**Examples:**
- Application startup/shutdown
- Audio manager initialization
- Spotify login status changes
- Mute state toggles

### ⚠️ Warning Level (Production + Debug)
- Non-critical issues that don't prevent operation
- Fallback operations being used
- Missing optional resources
- Performance concerns
- Recoverable errors

**Examples:**
- Missing icon files
- Audio sessions not found
- Cookie access errors
- UI thread violations

### ❌ Error Level (Production + Debug)
- Critical failures that impact functionality
- Unhandled exceptions
- System errors
- User-impacting issues
- Fatal initialization problems

**Examples:**
- WebView2 initialization failures
- Audio system crashes
- File access denied errors
- Network connectivity issues

## Debug Features

### Log File Creation
In debug mode, logs are automatically saved to:
```
%APPDATA%\VYS Browser\debug.log
```

### Detailed Format
Debug mode includes:
- **Timestamps**: Precise timing with milliseconds
- **Log Levels**: Visual icons and level names
- **Categories**: Component-specific categorization
- **Source Location**: Method names and line numbers
- **Thread Safety**: Concurrent access protection

### Performance Impact
- **Production Mode**: Minimal overhead, only essential logging
- **Debug Mode**: Full logging with file I/O, suitable for development

## Components

### Core Logging System
- **File**: `vys/Logging/Logger.cs`
- **Features**: Thread-safe, configurable, file output
- **Initialization**: Automatic via command line parsing

### Updated Components
1. **Application** (`App.xaml.cs`)
   - Command line argument parsing
   - Logger initialization
   - Application lifecycle logging

2. **MainWindow** (`MainWindow.xaml.cs`)
   - UI interaction logging
   - Constructor and initialization tracking
   - User action logging

3. **AudioManager** (`Audio/AudioManager.cs`)
   - Audio system initialization
   - Mute state changes
   - Settings persistence

4. **WindowsAudioApi** (`Audio/WindowsAudioApi.cs`)
   - Low-level audio API operations
   - Device initialization
   - Session management

5. **SpotifyLoginDetector** (`SpotifyLoginDetector.cs`)
   - Login status checking
   - Cookie validation
   - Authentication state changes

## Migration Status

### ✅ Fully Migrated
- Application startup/shutdown
- Error handling and exceptions
- User-facing operations
- Critical system events
- Configuration changes

### 🔄 Partially Migrated
- Audio session monitoring (some Console.WriteLine remain)
- WebView2 process tracking (mixed logging)
- Continuous monitoring loops (gradual conversion)

### 📋 Migration Strategy
The logging system was implemented with a gradual migration approach:
1. **Core infrastructure** established first
2. **Critical paths** converted to new system
3. **Detailed monitoring** being converted incrementally
4. **Backward compatibility** maintained during transition

## Troubleshooting

### Common Issues

#### No Debug Output
- Ensure you're using the `--debug` flag
- Check that the application started successfully
- Verify command line argument parsing

#### Log File Not Created
- Check permissions for `%APPDATA%\VYS Browser\`
- Ensure debug mode is enabled
- Look for initialization error messages

#### Mixed Output Formats
- This is expected during the migration period
- Some components still use Console.WriteLine
- Gradual conversion preserves functionality

### Debug Mode Benefits
- **Complete Operation Tracking**: See every step of audio muting
- **Timing Information**: Identify performance bottlenecks
- **Error Context**: Full stack traces and error details
- **State Validation**: Verify internal state consistency
- **Integration Testing**: Monitor component interactions

## Best Practices

### For End Users
- Use production mode for normal operation
- Enable debug mode only when troubleshooting
- Share debug logs when reporting issues

### For Developers
- Use appropriate log levels for different message types
- Include relevant context in log messages
- Test both production and debug modes
- Monitor log file size in debug mode

### For Troubleshooting
- Always start with debug mode for issue investigation
- Check both console output and log file
- Look for error patterns and timing issues
- Use log levels to filter relevant information

## Future Enhancements

### Planned Improvements
- Complete migration of remaining Console.WriteLine statements
- Log rotation and size management
- Remote logging capabilities
- Performance metrics integration
- Configuration file support

### Extensibility
The logging system is designed to be easily extended with:
- Additional log levels
- Custom output formats
- Multiple output destinations
- Filtering and search capabilities
