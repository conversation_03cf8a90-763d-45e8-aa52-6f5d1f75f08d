<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebView2 Console Logging Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background-color: #005a9e;
        }
        .info {
            background-color: #e7f3ff;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 WebView2 Console Logging Test</h1>
        
        <div class="info">
            <strong>Instructions:</strong> Click the buttons below to test different console message types. 
            When running the VYS Browser in debug mode (--debug flag), these messages should appear 
            in the C# application logs with appropriate categorization.
        </div>

        <div class="test-section">
            <h3>📝 Basic Console Messages</h3>
            <button onclick="testBasicLogs()">Test Basic Logs</button>
            <button onclick="testInfoLogs()">Test Info Logs</button>
            <button onclick="testDebugLogs()">Test Debug Logs</button>
        </div>

        <div class="test-section">
            <h3>⚠️ Warning and Error Messages</h3>
            <button onclick="testWarnings()">Test Warnings</button>
            <button onclick="testErrors()">Test Errors</button>
            <button onclick="testAsserts()">Test Assertions</button>
        </div>

        <div class="test-section">
            <h3>🔄 Advanced Testing</h3>
            <button onclick="testAllLevels()">Test All Levels</button>
            <button onclick="testWithObjects()">Test with Objects</button>
            <button onclick="testRapidFire()">Rapid Fire Test</button>
        </div>

        <div class="test-section">
            <h3>📊 Test Results</h3>
            <div id="results"></div>
        </div>
    </div>

    <script>
        let testCounter = 0;

        function logResult(message) {
            const results = document.getElementById('results');
            results.innerHTML += `<div>${new Date().toLocaleTimeString()}: ${message}</div>`;
        }

        function testBasicLogs() {
            console.log('🔵 Basic console.log message #' + (++testCounter));
            logResult('Sent basic log message');
        }

        function testInfoLogs() {
            console.info('ℹ️ Console.info message #' + (++testCounter));
            logResult('Sent info message');
        }

        function testDebugLogs() {
            console.debug('🐛 Console.debug message #' + (++testCounter));
            logResult('Sent debug message');
        }

        function testWarnings() {
            console.warn('⚠️ Console.warn message #' + (++testCounter));
            logResult('Sent warning message');
        }

        function testErrors() {
            console.error('❌ Console.error message #' + (++testCounter));
            logResult('Sent error message');
        }

        function testAsserts() {
            console.assert(false, '🚨 Console.assert failed message #' + (++testCounter));
            logResult('Sent assertion message');
        }

        function testAllLevels() {
            console.log('🔵 Log level test #' + (++testCounter));
            console.info('ℹ️ Info level test #' + testCounter);
            console.debug('🐛 Debug level test #' + testCounter);
            console.warn('⚠️ Warning level test #' + testCounter);
            console.error('❌ Error level test #' + testCounter);
            console.trace('📍 Trace level test #' + testCounter);
            logResult('Sent all log levels');
        }

        function testWithObjects() {
            const testObj = {
                id: ++testCounter,
                name: 'Test Object',
                data: [1, 2, 3, 4, 5],
                nested: { value: 'nested data' }
            };
            console.log('📦 Object logging test:', testObj);
            console.warn('⚠️ Warning with object:', { warning: 'test warning', counter: testCounter });
            logResult('Sent messages with objects');
        }

        function testRapidFire() {
            for (let i = 1; i <= 5; i++) {
                setTimeout(() => {
                    console.log(`🚀 Rapid fire message ${i}/5 (test #${++testCounter})`);
                    if (i === 5) {
                        logResult('Completed rapid fire test');
                    }
                }, i * 100);
            }
        }

        // Initial message when page loads
        console.log('🎯 WebView2 Console Logging Test Page Loaded');
        logResult('Test page initialized');
    </script>
</body>
</html>
